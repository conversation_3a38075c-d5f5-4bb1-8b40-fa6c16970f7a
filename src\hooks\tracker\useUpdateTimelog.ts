import { useMutation, useQueryClient, UseMutationOptions } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { MutationContext, PreviousTimelogs, TimelogData, UpdateTimelogParams } from "@/types/tracker";
import ToastMessage from "@/components/toast/toast.component";

export const useUpdateTimelog = () => {
  const queryClient = useQueryClient();
  const timelogResource = useMemo(() => new Resource("timelogs"), []);

  // Define mutation options with proper types
  const mutationOptions: UseMutationOptions<TimelogData, Error, UpdateTimelogParams, MutationContext> = {
    mutationFn: async ({ timelogId, updateData }) => {
      // For historical data updates, we need to send the full object
      const isHistoricalUpdate = "id" in updateData && updateData.id === timelogId;

      if (isHistoricalUpdate) {
        // For historical updates, we expect the full timelog object
        const response = await timelogResource.update(timelogId, updateData);
        return response.data;
      } else {
        // For active timers, we can do a partial update
        const response = await timelogResource.patch(timelogId, updateData);
        return response.data;
      }
    },
    onMutate: async ({ timelogId, updateData }): Promise<MutationContext> => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ["weeklyTimelogs"] });
      await queryClient.cancelQueries({ queryKey: ["timelogs"] });

      // Snapshot the previous value
      const previousTimelogs = queryClient.getQueryData<PreviousTimelogs>(["weeklyTimelogs"]);

      // Optimistically update the cache
      if (previousTimelogs) {
        const updatedPages = previousTimelogs.pages.map(page =>
          page.map(week => ({
            ...week,
            logs: week.logs.map(log => (log.id === timelogId ? { ...log, ...updateData } : log)),
          }))
        );

        queryClient.setQueryData(["weeklyTimelogs"], { pages: updatedPages });
      }

      return { previousTimelogs };
    },
    onSuccess: (_data, variables) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["weeklyTimelogs"] });
      queryClient.invalidateQueries({ queryKey: ["timelogs"] });

      // Show success message for non-silent updates
      if (!variables.silent) {
        ToastMessage.success("Time entry updated successfully");
      }
    },
    onError: (_error, variables, context) => {
      // Rollback on error
      if (context?.previousTimelogs) {
        queryClient.setQueryData(["weeklyTimelogs"], context.previousTimelogs);
      }

      // Show error message for non-silent updates
      if (!variables.silent) {
        ToastMessage.error("Failed to update time entry. Please try again.");
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ["weeklyTimelogs"] });
      queryClient.invalidateQueries({ queryKey: ["timelogs"] });
    },
  };

  // Create a type-safe mutation function
  const mutationFn = async (params: UpdateTimelogParams) => {
    const { timelogId, updateData } = params;
    // For historical data updates, we need to send the full object
    const isHistoricalUpdate = "id" in updateData && updateData.id === timelogId;

    if (isHistoricalUpdate) {
      // For historical updates, we expect the full timelog object
      const response = await timelogResource.update(timelogId, updateData);
      return response.data;
    } else {
      // For active timers, we can do a partial update
      const response = await timelogResource.patch(timelogId, updateData);
      return response.data;
    }
  };

  // Return the mutation with proper types
  return useMutation<TimelogData, Error, UpdateTimelogParams, MutationContext>({
    mutationFn,
    ...mutationOptions,
  });
};
