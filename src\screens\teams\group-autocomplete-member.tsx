import React, { useEffect, useMemo, useState } from "react";
import { Autocomplete, Box, CircularProgress, TextField } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";
import ToastMessage from "@/components/toast/toast.component";
import Resource from "@/core/api/resource";
import { GroupUserAutocompleteProps, IGroupUser } from "@/types";

const GroupUserAutocomplete: React.FC<GroupUserAutocompleteProps> = ({
  groupId,
  useGroupUsers,
  membersList = {},
  refetchMemberList,
  refetchGroupUsers,
}) => {
  const { data: groupData, isLoading: isLoadingGroupUsers } = useGroupUsers(groupId);
  const groupUsers: IGroupUser[] = useMemo(() => {
    return groupData?.results ?? [];
  }, [groupData?.results]);

  const staticUsers: IGroupUser[] = membersList?.data?.results ?? [];
  const allUsers: IGroupUser[] = Array.from(new Map([...staticUsers, ...groupUsers].map(u => [u.id, u])).values());

  const [selectedUsers, setSelectedUsers] = useState<IGroupUser[]>(groupUsers);

  useEffect(() => {
    setSelectedUsers(groupUsers);
  }, [groupData, groupUsers]);

  const groupsResource = new Resource("auth/groups");

  const groupUserModifyMutation = useMutation({
    mutationFn: async (user_ids: (string | number)[]) => {
      return groupsResource.custom(`${groupId}/users/modify/`, {
        method: "POST",
        data: { user_ids },
      });
    },
    onSuccess: () => {
      ToastMessage.success("Group users updated");
      refetchMemberList?.();
      refetchGroupUsers?.();
    },
    onError: (error: AxiosError) => {
      ToastMessage.error(error.message || "Failed to update group users");
    },
  });

  const handleUserChange = (_event: unknown, updatedUsers: IGroupUser[]) => {
    const newlyAdded = updatedUsers.filter(user => !groupUsers.some(u => u.id === user.id));

    if (newlyAdded.length > 0) {
      const updatedList = [...groupUsers, ...newlyAdded];
      setSelectedUsers(updatedList);
      groupUserModifyMutation.mutate(updatedList.map(user => user.id));
    } else {
      // Prevent removal — revert to groupUsers
      setSelectedUsers(groupUsers);
    }
  };

  return (
    <Box sx={{ width: 500 }}>
      <Autocomplete
        multiple
        disableClearable
        limitTags={2}
        id="group-user-autocomplete"
        options={allUsers}
        value={selectedUsers}
        onChange={handleUserChange}
        getOptionLabel={(user: IGroupUser) => `${user.first_name} ${user.last_name}`}
        isOptionEqualToValue={(option, value) => option.id === value.id}
        loading={isLoadingGroupUsers}
        disabled={groupUserModifyMutation.isPending}
        renderTags={value =>
          value.map(option => (
            <Box
              key={option.id}
              component="span"
              sx={{
                display: "inline-flex",
                alignItems: "center",
                padding: "4px 8px",
                backgroundColor: "#e0e0e0",
                borderRadius: "16px",
                margin: "2px",
                fontSize: "0.875rem",
              }}
            >
              {option.first_name} {option.last_name}
            </Box>
          ))
        }
        renderInput={params => {
          const { InputProps, ...rest } = params;
          return (
            <TextField
              {...rest}
              label="Add Users"
              placeholder="Users"
              slotProps={{
                input: {
                  ...InputProps,
                  endAdornment: (
                    <>
                      {isLoadingGroupUsers && <CircularProgress size={20} />}
                      {InputProps?.endAdornment}
                    </>
                  ),
                },
              }}
            />
          );
        }}
      />
    </Box>
  );
};

export default GroupUserAutocomplete;
