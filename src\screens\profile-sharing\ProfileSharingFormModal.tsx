// import React, { useState, useEffect } from "react";
// import { <PERSON><PERSON>ield, Grid } from "@mui/material";
// import GenericModal from "@/components/modal/generic-modal.component";
// import {
//   IProfileShare,
//   IProfileShareInput,
// } from "@/interfaces/profileSharing.interface";
// import { ModalSetting } from "@/interfaces/modal-props.interface";
// import GenericFormControl from "@/components/form-control/generic-form.component";
// import GenericCheckbox from "@/components/form-control/generic-checkbox.component";
// import GenericDatePicker from "@/components/form-control/generic-date-picker.component";
// import GenericActionButtons from "@/components/form-control/generic-action-buttons.component";

// interface ProfileSharingFormModalProps {
//   open: boolean;
//   onClose: () => void;
//   onSave: (profileShare: IProfileShare | IProfileShareInput) => void;
//   profileShare: IProfileShare | null;
// }

// const ProfileSharingFormModal: React.FC<ProfileSharingFormModalProps> = ({
//   open,
//   onClose,
//   onSave,
//   profileShare,
// }) => {
//   const [formData, setFormData] = useState<Partial<IProfileShareInput>>({
//     profileId: "user123",
//     isActive: true,
//   });
//   const [expires, setExpires] = useState<boolean>(false);

//   const modalSetting: ModalSetting = {
//     open,
//     onClose,
//   };

//   useEffect(() => {
//     if (profileShare) {
//       setFormData({
//         profileId: profileShare.profileId,
//         sharedWithEmail: profileShare.sharedWithEmail,

//         expiresAt: profileShare.expiresAt
//           ? new Date(profileShare.expiresAt)
//           : undefined,
//         isActive: profileShare.isActive,
//         notes: profileShare.notes,
//       });
//       setExpires(!!profileShare.expiresAt);
//     } else {
//       setFormData({
//         profileId: "user123",

//         isActive: true,
//         sharedWithEmail: "",

//         notes: "",
//         expiresAt: undefined,
//       });
//       setExpires(false);
//     }
//   }, [profileShare, open]);

//   const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const { name, value, type } = event.target;
//     if (type === "checkbox") {
//       setFormData((prev) => ({ ...prev, [name]: event.target.checked }));
//     } else {
//       setFormData((prev) => ({ ...prev, [name]: value }));
//     }
//   };

//   const handleDateChange = (date: Date | null) => {
//     setFormData((prev) => ({ ...prev, expiresAt: date || undefined }));
//   };

//   const handleSave = () => {
//     const dataToSave: IProfileShareInput | IProfileShare = {
//       ...(profileShare || {}),
//       ...formData,
//       accessLevel: "view",
//       expiresAt: expires ? formData.expiresAt : undefined,
//     } as IProfileShareInput | IProfileShare;

//     if (!dataToSave.profileId) {
//       alert("Profile ID is required.");
//       return;
//     }

//     onSave(dataToSave);
//   };

//   return (
//     <GenericModal
//       setting={modalSetting}
//       title={profileShare ? "Edit Share Link" : "Create New Share Link"}
//     >
//       <Grid container spacing={2} sx={{ pt: 1 }}>
//         <Grid item xs={12}>
//           <GenericFormControl
//             id="sharedWithEmail"
//             label="Share with Email (Optional)"
//             type="email"
//             value={formData.sharedWithEmail || ""}
//             onChangeFn={handleChange}
//             helperText="Leave blank to create a general shareable link."
//             placeholder="Enter email address"
//           />
//         </Grid>

//         <Grid item xs={12} sm={6}>
//           <GenericCheckbox
//             label="Link Active"
//             checked={formData.isActive || false}
//             onChange={handleChange}
//             name="isActive"
//           />
//         </Grid>

//         <Grid item xs={12}>
//           <GenericCheckbox
//             label="Set Expiration Date"
//             checked={expires}
//             onChange={(e) => setExpires(e.target.checked)}
//           />
//         </Grid>

//         {expires && (
//           <Grid item xs={12}>
//             <GenericDatePicker
//               label="Expires At"
//               value={formData.expiresAt ? new Date(formData.expiresAt) : null}
//               onChange={handleDateChange}
//             />
//           </Grid>
//         )}

//         <Grid item xs={12}>
//           <TextField
//             name="notes"
//             label="Notes (Optional)"
//             value={formData.notes || ""}
//             onChange={handleChange}
//             fullWidth
//             multiline
//             rows={3}
//             variant="outlined"
//           />
//         </Grid>
//       </Grid>

//       <GenericActionButtons
//         buttons={[
//           {
//             label: "Cancel",
//             onClick: onClose,
//             color: "secondary",
//           },
//           {
//             label: profileShare ? "Save Changes" : "Create Link",
//             onClick: handleSave,
//             variant: "contained",
//             color: "primary",
//           },
//         ]}
//       />
//     </GenericModal>
//   );
// };

// export default ProfileSharingFormModal;
