import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { TaskData } from "@/types/tracker";

export const useClickupTaskById = (spaceId: string | undefined | null, taskId: string | undefined | null) => {
  const clickupTaskResource = useMemo(() => new Resource("integrations/clickup-tasks"), []);

  // Only fetch if we have both spaceId and taskId
  const shouldFetch = useMemo(() => {
    return !!spaceId && !!taskId;
  }, [spaceId, taskId]);

  const query = useQuery({
    queryKey: ["clickupTaskById", spaceId, taskId],
    queryFn: async () => {
      // Search for the specific task ID
      const response = await clickupTaskResource.list({
        space_id: spaceId,
        task_id: taskId, // Use the task ID as search term
        page: 1,
        page_size: 10,
      });

      // Find the exact task by ID from the results
      const exactTask = response.data.results?.find((task: TaskData) => task.task_id === taskId);

      return exactTask || null;
    },
    enabled: shouldFetch,
    retry: 2,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
  });

  return {
    task: query.data,
    isLoading: query.isPending,
    error: query.error,
    isError: query.isError,
    refetch: query.refetch,
  };
};
