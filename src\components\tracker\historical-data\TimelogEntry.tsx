import React, { useState } from "react";
import { <PERSON>I<PERSON>, ListItemText, Typography, Box, Chip, IconButton, Tooltip } from "@mui/material";
import {
  AttachMoney as AttachMoneyIcon,
  AccessTime as AccessTimeIcon,
  Task as TaskIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { format, differenceInSeconds } from "date-fns";
import { TimelogEntryProps } from "@/types/tracker";
import { useDeleteTimelog } from "@/hooks/tracker";
import { useProjects } from "@/hooks/tracker/useProjects";
import { formatTime } from "@/utils/time";
import DeleteConfirmationDialog from "@/components/tracker/DeleteConfirmationDialog";

const TimelogEntry: React.FC<TimelogEntryProps> = React.memo(({ log }) => {
  // Delete state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Get project details for this log - memoized to prevent unnecessary re-fetches
  const { data } = useProjects("", { ids: log.project ? [log.project] : [] });
  const deleteTimelogMutation = useDeleteTimelog();

  const projectName = React.useMemo(() => {
    if (!data?.pages || !log.project) return "Unknown Project";
    const project = data.pages.flatMap(page => page.results).find(p => p.id === log.project);
    return project?.name || "Unknown Project";
  }, [data, log.project]);

  // Calculate duration if both start and end times are available - memoized for performance
  const duration = React.useMemo(() => {
    if (!log.end_time) return "Running";

    try {
      const startTime = new Date(log.start_time);
      const endTime = new Date(log.end_time);

      // Validate dates
      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
        return "Invalid";
      }

      const durationInSeconds = differenceInSeconds(endTime, startTime);

      // Ensure non-negative duration
      if (durationInSeconds < 0) {
        return "Invalid";
      }

      return formatTime(durationInSeconds);
    } catch {
      return "Invalid";
    }
  }, [log.start_time, log.end_time]);

  // Format time range - memoized for performance
  const timeRange = React.useMemo(() => {
    try {
      const startTime = format(new Date(log.start_time), "HH:mm");
      const endTime = log.end_time ? format(new Date(log.end_time), "HH:mm") : "Running";
      return `${startTime} - ${endTime}`;
    } catch {
      return "Invalid time";
    }
  }, [log.start_time, log.end_time]);

  // Delete handlers
  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = () => {
    deleteTimelogMutation.mutate(log.id, {
      onSuccess: () => {
        setShowDeleteDialog(false);
      },
      onError: () => {
        setShowDeleteDialog(false);
      },
    });
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  return (
    <>
      <ListItem
        divider
        sx={{
          py: { xs: 1, sm: 1.5 },
          display: "flex",
          alignItems: "flex-start",
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 1, sm: 0 },
        }}
      >
        <ListItemText
          primary={
            <Typography variant="body2" component="div" sx={{ fontWeight: 500 }}>
              {timeRange}
            </Typography>
          }
          secondary={
            <Box component="div" sx={{ mt: 0.5 }}>
              <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
                {projectName}
              </Typography>
              {log.description && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{
                    display: "block",
                    mt: 0.25,
                    lineHeight: 1.3,
                  }}
                >
                  {log.description}
                </Typography>
              )}
            </Box>
          }
          sx={{
            flex: 1,
            minWidth: 0, // Allow text truncation
          }}
        />

        <Box
          display="flex"
          alignItems="center"
          sx={{
            ml: { xs: 0, sm: 2 },
            flexShrink: 0,
            gap: 0.5,
            flexWrap: { xs: "wrap", sm: "nowrap" },
            justifyContent: { xs: "flex-start", sm: "flex-end" },
            width: { xs: "100%", sm: "auto" },
          }}
        >
          {/* Status Indicators */}
          {log.is_billable && (
            <Chip
              icon={<AttachMoneyIcon />}
              label="Billable"
              size="small"
              color="success"
              variant="outlined"
              aria-label="This time entry is billable"
            />
          )}

          {log.is_ot && (
            <Chip
              icon={<AccessTimeIcon />}
              label="OT"
              size="small"
              color="warning"
              variant="outlined"
              aria-label="This time entry is overtime"
            />
          )}

          {log.clickup_task_id && (
            <Chip
              icon={<TaskIcon />}
              label="ClickUp"
              size="small"
              color="info"
              variant="outlined"
              aria-label="This time entry is linked to a ClickUp task"
            />
          )}

          {/* Duration */}
          <Typography
            variant="body2"
            sx={{
              minWidth: { xs: "auto", sm: "80px" },
              textAlign: { xs: "left", sm: "right" },
              fontWeight: 500,
              ml: { xs: 0, sm: 1 },
            }}
            aria-label={`Duration: ${duration}`}
          >
            {duration}
          </Typography>

          {/* Action buttons */}
          <Box
            sx={{
              width: { xs: 0, sm: "40px" },
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Tooltip title="Delete">
              <IconButton onClick={handleDelete} size="small" color="error">
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </ListItem>
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        loading={deleteTimelogMutation.isPending}
      />
    </>
  );
});

// Custom comparison function for React.memo to optimize re-renders
const areEqual = (prevProps: TimelogEntryProps, nextProps: TimelogEntryProps) => {
  // Compare all relevant properties of the log
  return (
    prevProps.log.id === nextProps.log.id &&
    prevProps.log.start_time === nextProps.log.start_time &&
    prevProps.log.end_time === nextProps.log.end_time &&
    prevProps.log.project === nextProps.log.project &&
    prevProps.log.description === nextProps.log.description &&
    prevProps.log.is_billable === nextProps.log.is_billable &&
    prevProps.log.is_ot === nextProps.log.is_ot &&
    prevProps.log.clickup_task_id === nextProps.log.clickup_task_id
  );
};

TimelogEntry.displayName = "TimelogEntry";

export default React.memo(TimelogEntry, areEqual);
