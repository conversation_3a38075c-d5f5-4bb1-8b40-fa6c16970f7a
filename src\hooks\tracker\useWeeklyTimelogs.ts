import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { UseWeeklyTimelogsParams, WeeklyTimelogsResponse } from "@/types/tracker";
import { startOfWeek, endOfWeek, addDays } from "date-fns";

const fetchWeeklyTimelogs = async (
  resource: Resource,
  params: {
    user?: string;
    user__in?: string;
    page?: number;
    page_size?: number;
    start_time__gte?: string;
    end_time__lte?: string;
    paginate?: boolean;
    project?: string;
    project__in?: string;
    is_billable?: boolean;
    is_ot?: boolean;
  }
): Promise<WeeklyTimelogsResponse> => {
  // Filter out undefined parameters
  const queryParams = Object.fromEntries(Object.entries(params).filter(([, value]) => value !== undefined)) as Record<
    string,
    string | number | boolean
  >;

  const response = await resource.list(queryParams);
  let data = response.data;

  // If the response is an array (when paginate is true), wrap it in an object with a results property
  if (Array.isArray(data)) {
    data = { results: data };
  }

  // Sort results by week descending if API doesn't guarantee it
  if (data.results) {
    data.results.sort(
      (a: { week: string }, b: { week: string }) => new Date(b.week).getTime() - new Date(a.week).getTime()
    );

    // Filter out any logs that might be active (where end_time is null)
    // This is for historical data only
    data.results = data.results.map((week: { logs: { end_time: string | null }[] }) => ({
      ...week,
      logs: week.logs.filter((log: { end_time: string | null }) => log.end_time != null),
    }));
  }

  return data;
};

export const useWeeklyTimelogs = ({
  userId,
  page = 1,
  pageSize = 10,
  start_time__gte = undefined,
  end_time__lte = undefined,
  paginate = true,
  project = undefined,
  project__in = undefined,
  is_billable = undefined,
  is_ot = undefined,
  user__in = undefined,
  enabled = true,
}: UseWeeklyTimelogsParams) => {
  const timelogResource = useMemo(() => new Resource("timelogs/weekly"), []);

  // Generate query key based on all parameters
  const queryKey = useMemo(
    () =>
      [
        "weeklyTimelogs",
        userId,
        user__in,
        paginate ? page : null,
        paginate ? pageSize : null,
        start_time__gte,
        end_time__lte,
        paginate,
        project,
        project__in,
        is_billable,
        is_ot,
      ].filter(value => {
        // Keep all values except undefined/null, but preserve false for boolean params
        return value !== undefined && value !== null;
      }),
    [
      userId,
      user__in,
      page,
      pageSize,
      start_time__gte,
      end_time__lte,
      paginate,
      project,
      project__in,
      is_billable,
      is_ot,
    ]
  );

  return useQuery({
    queryKey,
    queryFn: () =>
      fetchWeeklyTimelogs(timelogResource, {
        ...(userId && { user: userId }),
        ...(user__in && { user__in: user__in.join(",") }),
        ...(paginate && { page, page_size: pageSize }),
        ...(start_time__gte && { start_time__gte }),
        ...(end_time__lte && { end_time__lte }),
        ...(project && { project }),
        ...(project__in && { project__in }),
        ...(is_billable !== undefined && { is_billable }),
        ...(is_ot !== undefined && { is_ot }),
        paginate,
      }),
    enabled,
    placeholderData: keepPreviousData,
    staleTime: 0,
    refetchInterval: 10000, // Refetch every 10 seconds to balance performance and synchronization
    refetchOnWindowFocus: true,
  });
};

// Helper function to get the start and end of a week for a given date
export const getWeekRange = (date: Date = new Date()) => {
  const start = startOfWeek(date, { weekStartsOn: 1 }); // Week starts on Monday
  const end = endOfWeek(date, { weekStartsOn: 1 });
  return { start, end };
};

// Helper function to get the previous week's range
export const getPreviousWeekRange = (startDate: Date) => {
  const prevWeekStart = addDays(startDate, -7);
  return getWeekRange(prevWeekStart);
};

// Helper function to get the next week's range
export const getNextWeekRange = (endDate: Date) => {
  const nextWeekStart = addDays(endDate, 1);
  return getWeekRange(nextWeekStart);
};
