import { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import { AUTH_REFRESH_TOKEN } from "@/core/http/endpoint-urls";
import { getCookie } from "@/utils/utils";
import AuthResource from "@/core/api/auth";
import http from "@/core/http/http";
const RETRY_COUNT_LIMIT = 3;
const XCRFTOKEN = "X-CSRFToken";

// Token refresh queue to prevent multiple simultaneous refresh calls
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: unknown) => void;
  reject: (reason?: unknown) => void;
}> = [];

const processQueue = (error: unknown, token: unknown = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

const requestInterceptor = (request: InternalAxiosRequestConfig) => {
  const csrf_cookie = getCookie("csrftoken");
  if (csrf_cookie && request.method?.toLocaleLowerCase() !== "get") {
    request.headers[XCRFTOKEN] = csrf_cookie;
  }
  return request;
};

const responseInterceptor = (response: AxiosResponse) => {
  return response;
};

const errorInterceptor = async (error: AxiosError<{ error: { message: string } }>) => {
  if (!error.response) {
    return Promise.reject(error);
  }

  const originalRequest = error.config as InternalAxiosRequestConfig & {
    _retry?: boolean;
    __isRetryRequest?: boolean;
    retryCount: number;
  };
  const authResource = new AuthResource();

  // Prevent redirect for login requests (e.g., /auth/login) on 401
  const isLoginRequest = originalRequest.url?.includes("/login");

  if (originalRequest.url === AUTH_REFRESH_TOKEN && error.response.status === 401) {
    // Clear the queue and redirect if refresh token is invalid
    processQueue(error, null);
    isRefreshing = false;
    window.location.href = "/login";
    return Promise.reject(error);
  }

  if (error.response.status === 401 && !originalRequest._retry && !isLoginRequest) {
    if (isRefreshing) {
      // If refresh is already in progress, queue this request
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject });
      })
        .then(() => {
          // Retry the original request after refresh completes
          return http.request(originalRequest);
        })
        .catch(err => {
          return Promise.reject(err);
        });
    }

    originalRequest._retry = true;
    // Initialize retry count for this specific request
    originalRequest.retryCount = originalRequest.retryCount || 0;
    originalRequest.retryCount++;

    // Only redirect if THIS specific request has exceeded retry limit
    if (originalRequest.retryCount > RETRY_COUNT_LIMIT) {
      window.location.href = "/login";
      return Promise.reject(error);
    }

    isRefreshing = true;

    try {
      await authResource.refreshUser();
      processQueue(null, true);
      isRefreshing = false;
      return http.request(originalRequest);
    } catch (refreshError) {
      processQueue(refreshError, null);
      isRefreshing = false;
      window.location.href = "/login";
      return Promise.reject(refreshError);
    }
  }
  return Promise.reject(error);
};

export { errorInterceptor, requestInterceptor, responseInterceptor };
