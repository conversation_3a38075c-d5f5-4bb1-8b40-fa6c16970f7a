import {
  IEmployeeProfile,
  IEmployeeProfilePayload,
  IProfileDetailNode,
} from "@/interfaces/employeeProfile.interface";
import { createWorkExperienceDuration } from "@/utils/work-experience-utils";

// Define the structure that matches backend expectations (without IDs)
export interface DetailNode {
  id?: string; // Optional unique identifier for React keys
  value: string;
  children?: DetailNode[];
}

export interface FormValues {
  user_id: string;
  details: DetailNode[];
}

/**
 * Creates the initial form structure with all required sections
 */
export const createInitialFormValues = (): FormValues => ({
  user_id: "",
  details: [
    {
      value: "Background",
      children: [
        { value: "core language", children: [] },
        { value: "core framework", children: [] },
        { value: "additional skills", children: [] },
      ],
    },
    {
      value: "Languages",
      children: [],
    },
    {
      value: "Education",
      children: [],
    },
    {
      value: "Work Experience",
      children: [],
    },
    {
      value: "Availability",
      children: [],
    },
  ],
});

/**
 * Converts an employee profile to form values for editing
 */
export const mapProfileToFormValues = (
  profile: IEmployeeProfile,
): FormValues => {
  const formValues = createInitialFormValues();
  formValues.user_id = profile.selectedEmployee.id;

  // Map Background section
  const backgroundSection = formValues.details.find(
    (d) => d.value === "Background",
  );
  if (backgroundSection && profile.candidateBackground) {
    const coreLanguageGroup = backgroundSection.children?.find(
      (c) => c.value === "core language",
    );
    const coreFrameworkGroup = backgroundSection.children?.find(
      (c) => c.value === "core framework",
    );
    const additionalSkillsGroup = backgroundSection.children?.find(
      (c) => c.value === "additional skills",
    );

    if (coreLanguageGroup) {
      coreLanguageGroup.children =
        profile.candidateBackground.coreLanguages.map((lang) => ({
          value: lang,
        }));
    }
    if (coreFrameworkGroup) {
      coreFrameworkGroup.children =
        profile.candidateBackground.coreFrameworks.map((framework) => ({
          value: framework,
        }));
    }
    if (additionalSkillsGroup) {
      additionalSkillsGroup.children =
        profile.candidateBackground.additionalSkills.map((skill) => ({
          value: skill,
        }));
    }
  }

  // Map Languages section
  const languagesSection = formValues.details.find(
    (d) => d.value === "Languages",
  );
  if (languagesSection && profile.languages) {
    languagesSection.children = profile.languages.map((lang) => ({
      value: lang.language,
      children: [{ value: lang.proficiency }],
    }));
  }

  // Map Education section
  const educationSection = formValues.details.find(
    (d) => d.value === "Education",
  );
  if (educationSection && profile.education) {
    educationSection.children = profile.education.map((edu) => ({
      value: edu.institution,
      children: [
        { value: edu.degree },
        ...(edu.fieldOfStudy ? [{ value: edu.fieldOfStudy }] : []),
        ...(edu.graduationYear ? [{ value: edu.graduationYear }] : []),
      ],
    }));
  }

  // Map Work Experience section
  const workSection = formValues.details.find(
    (d) => d.value === "Work Experience",
  );
  if (workSection && profile.workExperience) {
    workSection.children = profile.workExperience.map((work) => {
      const durationString = createWorkExperienceDuration(work);

      return {
        value: `${work.company}${work.location ? `, ${work.location}` : ""}`,
        children: [
          { value: "role", children: [{ value: work.role }] },
          { value: "duration", children: [{ value: durationString }] },
          {
            value: "description",
            children: [{ value: work.responsibilities }],
          },
          ...(work.technologies && work.technologies.length > 0
            ? [
                {
                  value: "technologies",
                  children: work.technologies.map((tech) => ({ value: tech })),
                },
              ]
            : []),
        ],
      };
    });
  }

  // Map Availability section
  const availabilitySection = formValues.details.find(
    (d) => d.value === "Availability",
  );
  if (availabilitySection && profile.availability) {
    availabilitySection.children = [{ value: profile.availability }];
  }

  return formValues;
};

/**
 * Creates a new work experience entry structure
 */
export const createNewWorkExperience = (): DetailNode => ({
  id: `work-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // Generate unique ID
  value: "", // company
  children: [
    { value: "role", children: [{ value: "" }] },
    { value: "duration", children: [{ value: "" }] },
    { value: "description", children: [{ value: "" }] },
    { value: "technologies", children: [] },
  ],
});

/**
 * Converts form values to backend format
 */
export const convertFormValuesToBackendFormat = (
  values: FormValues,
): IEmployeeProfilePayload => {
  const convertToBackendFormat = (
    details: DetailNode[],
  ): IProfileDetailNode[] => {
    return details.map((detail, index) => ({
      id: undefined, // Will be assigned by backend
      value: detail.value,
      parent: null,
      order: index + 1,
      children: detail.children
        ? convertChildrenToBackendFormat(detail.children)
        : [],
    }));
  };

  const convertChildrenToBackendFormat = (
    children: DetailNode[],
  ): IProfileDetailNode[] => {
    return children.map((child, index) => ({
      id: undefined, // Will be assigned by backend
      value: child.value,
      parent: null, // Will be assigned by backend
      order: index + 1,
      children: child.children
        ? convertChildrenToBackendFormat(child.children)
        : [],
    }));
  };

  return {
    user_id: values.user_id,
    details: convertToBackendFormat(values.details),
  };
};
