import { useMutation } from "@tanstack/react-query";
import { useMemo } from "react";
import { format } from "date-fns";
import Resource from "@/core/api/resource";
import ToastMessage from "@/components/toast/toast.component";
import { ExportTimelogsParams } from "@/types/reports";

export const useExportTimelogs = () => {
  const exportResource = useMemo(() => new Resource("timelogs/report"), []);

  const exportTimelogs = async (params: ExportTimelogsParams): Promise<void> => {
    // Filter out undefined parameters and format them for the API
    const queryParams: Record<string, string | boolean> = {};

    if (params.user__in && params.user__in.length > 0) {
      queryParams.user__in = params.user__in.join(",");
    }

    if (params.start_time__gte) {
      queryParams.start_time__gte = params.start_time__gte;
    }

    if (params.end_time__lte) {
      queryParams.end_time__lte = params.end_time__lte;
    }

    if (params.project__in && params.project__in.length > 0) {
      queryParams.project__in = params.project__in.join(",");
    }

    if (params.is_billable) {
      queryParams.is_billable = true;
    }

    if (params.is_ot) {
      queryParams.is_ot = true;
    }

    try {
      // Use the Resource custom method for the export endpoint
      const response = await exportResource.custom("export/", {
        method: "get",
        params: queryParams,
        responseType: "blob",
      });

      // Create blob URL and trigger download
      const blob = new Blob([response.data], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);

      // Create temporary anchor element to trigger download
      const link = document.createElement("a");
      link.href = url;

      // Generate filename with current timestamp
      const timestamp = format(new Date(), "yyyy-MM-dd_HH-mm-ss");
      link.download = `timelog_report_${timestamp}.csv`;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ToastMessage.success("Report exported successfully!");
    } catch (error) {
      ToastMessage.error("Failed to export report. Please try again.");
      throw error;
    }
  };

  return useMutation({
    mutationFn: exportTimelogs,
  });
};
