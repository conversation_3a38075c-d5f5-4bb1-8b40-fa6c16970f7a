import { useState } from "react";
import { Box, Divider, Paper, Typography, Button } from "@mui/material";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

import Resource from "@/core/api/resource";
import TeamAPI from "@/core/api/teams.service";
import TableComponent from "@/components/table/table.component";
import GroupUserAutocomplete from "./group-autocomplete-member";
import ToastMessage from "@/components/toast/toast.component";
import GenericModal from "@/components/modal/generic-modal.component";
import { IGroupUser } from "@/types";
import { AxiosError } from "axios";

const groupResources = new Resource("auth/groups");
const groupUsersResource = (groupId: string) => new Resource(`auth/groups/${groupId}/users`);

const useGroupUsers = (groupId: string) =>
  useQuery({
    queryKey: ["groupUsers", groupId],
    queryFn: async () => {
      const res = await groupUsersResource(groupId).list();
      return res.data;
    },
  });

const ViewGroupData = () => {
  const { id } = useParams<{ id: string }>();
  if (!id) throw new Error("Group ID is required.");

  const [filter, setFilter] = useState({
    page: 1,
    "page-size": 10,
    ordering: "",
    search: "",
  });

  const teamResources = new TeamAPI.TeamResources();
  const { data: viewGroup } = useQuery({
    queryKey: ["viewGroup", id],
    queryFn: async () => {
      const res = await groupResources.get(id);
      return res.data;
    },
  });

  const { data: groupUsersData, refetch: refetchGroupUsers } = useGroupUsers(id);
  const { data: membersList, refetch: refetchMemberList } = useQuery({
    queryKey: ["userTeams", filter],
    queryFn: () => teamResources.list(filter),
    placeholderData: undefined,
  });

  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [userToRemove, setUserToRemove] = useState<IGroupUser | null>(null);

  const groupUserModifyMutation = useMutation({
    mutationFn: async (user_ids: (string | number)[]) => {
      return groupResources.custom(`${id}/users/modify/`, {
        method: "POST",
        data: { user_ids },
      });
    },
    onSuccess: () => {
      ToastMessage.success("Group users updated");
      refetchGroupUsers();
      refetchMemberList?.();
    },
    onError: (error: AxiosError) => {
      ToastMessage.error(error.message || "Failed to update group users");
    },
  });

  const handleRemoveUser = (user: IGroupUser) => {
    setUserToRemove(user);
    setConfirmModalOpen(true);
  };

  const confirmRemoval = () => {
    if (!userToRemove || !groupUsersData?.results) return;

    const remainingUsers = groupUsersData.results.filter((user: IGroupUser) => user.id !== userToRemove.id);

    const remainingUserIds = remainingUsers.map((user: IGroupUser) => user.id);

    groupUserModifyMutation.mutate(remainingUserIds);

    setUserToRemove(null);
    setConfirmModalOpen(false);
  };

  const cancelRemoval = () => {
    setUserToRemove(null);
    setConfirmModalOpen(false);
  };

  const groupUsers: IGroupUser[] = groupUsersData?.results || [];
  const rows = groupUsers.map(user => ({
    id: user.id,
    first_name: user.first_name,
    last_name: user.last_name,
    email: user.email,
  }));

  const columns = [
    { field: "first_name", headerName: "First Name", flex: 1 },
    { field: "last_name", headerName: "Last Name", flex: 1 },
    { field: "email", headerName: "Email", flex: 2 },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" mb={3}>
        Group: {viewGroup?.name_display || "Loading..."}
      </Typography>

      <Paper elevation={2} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Assign/Update Group Members
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <GroupUserAutocomplete
          groupId={id}
          useGroupUsers={useGroupUsers}
          membersList={membersList}
          refetchMemberList={refetchMemberList}
          refetchGroupUsers={refetchGroupUsers}
        />
      </Paper>

      <Divider sx={{ my: 4 }} />

      <Paper elevation={2} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Assigned Members
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <TableComponent
          columns={columns}
          rows={rows}
          loading={!groupUsersData}
          rowCount={groupUsers.length}
          contentAlign="left"
          hideFooter={false}
          onPaginationChange={model =>
            setFilter(prev => ({
              ...prev,
              page: model.page + 1,
              "page-size": model.pageSize,
            }))
          }
          actions={[
            {
              label: "Remove",
              handler: data => {
                handleRemoveUser(data as IGroupUser);
              },
            },
          ]}
        />
      </Paper>

      <GenericModal setting={{ open: confirmModalOpen, onClose: cancelRemoval }} title="Confirm Removal" height="500px">
        <Typography mb={2}>Are you sure you want to remove the following user from the group?</Typography>
        <Typography variant="subtitle1" mb={2}>
          {userToRemove?.first_name} {userToRemove?.last_name}
        </Typography>
        <Box display="flex" justifyContent="flex-end" gap={2}>
          <Button onClick={cancelRemoval} variant="outlined">
            Cancel
          </Button>
          <Button onClick={confirmRemoval} variant="contained" color="error">
            Confirm
          </Button>
        </Box>
      </GenericModal>
    </Box>
  );
};

export default ViewGroupData;
