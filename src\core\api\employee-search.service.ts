import { IUser, IUsersResponse } from "./users.service";
import UsersService from "./users.service";

export interface IEmployeeSearchResult {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  department: string;
  jobTitle: string;
  contactNumber?: string;
  address?: string;
}

const usersResource = new UsersService.UsersResource();

const transformUserToEmployee = (user: IUser): IEmployeeSearchResult => ({
  id: user.id,
  firstName: user.first_name,
  lastName: user.last_name,
  email: user.email,
  department: user.groups?.[0]?.name_display || "Unknown",
  jobTitle: "Employee",
  contactNumber: undefined,
  address: undefined,
});

export class EmployeeSearchService {
  static async searchEmployees(
    query: string,
  ): Promise<IEmployeeSearchResult[]> {
    if (!query.trim()) {
      return this.getAllEmployees();
    }

    const response = await usersResource.getActiveUsers();
    const usersData = response.data as IUsersResponse;

    const searchTerm = query.toLowerCase();
    const filteredUsers = usersData.results.filter(
      (user) =>
        user.first_name.toLowerCase().includes(searchTerm) ||
        user.last_name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm) ||
        (user.groups?.[0]?.name_display || "")
          .toLowerCase()
          .includes(searchTerm),
    );

    return filteredUsers.map(transformUserToEmployee);
  }

  static async getEmployeeById(
    id: string,
  ): Promise<IEmployeeSearchResult | null> {
    const response = await usersResource.get(id);
    const user = response.data as IUser;

    if (!user) {
      return null;
    }

    return transformUserToEmployee(user);
  }

  static async getAllEmployees(): Promise<IEmployeeSearchResult[]> {
    const response = await usersResource.getActiveUsers();
    const usersData = response.data as IUsersResponse;

    return usersData.results.map(transformUserToEmployee);
  }
}
