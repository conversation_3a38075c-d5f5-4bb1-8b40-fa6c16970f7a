export interface IEducation {
  institution: string;
  degree: string;
  fieldOfStudy?: string;
  graduationYear?: string;
}

export interface IWorkExperience {
  company: string;
  location?: string;
  role: string;
  startDate: string;
  endDate?: string;
  isCurrent?: boolean;
  responsibilities: string;
  technologies?: string[];
}

export interface ICandidateBackground {
  coreLanguages: string[];
  coreFrameworks: string[];
  additionalSkills: string[];
}

export interface ISelectedEmployee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  department: string;
  jobTitle: string;
  contactNumber?: string;
  address?: string;
}

export interface ILanguage {
  language: string;
  proficiency: string;
}

export interface IEmployeeProfile {
  id: string;
  selectedEmployee: ISelectedEmployee;
  startDate: string;
  candidateBackground?: ICandidateBackground;
  languages?: ILanguage[];
  availability?: string;
  education?: IEducation[];
  workExperience?: IWorkExperience[];
  status: "active" | "terminated";
  createdAt: string;
  updatedAt: string;
}

export type IEmployeeProfileInput = Omit<IEmployeeProfile, "id" | "createdAt" | "updatedAt">;

export interface IProfileDetailNode {
  id?: string;
  value: string;
  parent?: string | null;
  order: number;
  children: IProfileDetailNode[];
}

export interface IEmployeeProfilePayload {
  user_id: string;
  details: IProfileDetailNode[];
}

export interface EmployeeProfileFormProps {
  open?: boolean;
  onClose?: () => void;
  onSave: (profileData: IEmployeeProfilePayload) => void | Promise<void>;
  employeeProfile: IEmployeeProfile | null;
  selectedEmployeeId?: string;
  modal?: boolean;
}

export interface ValidationItem {
  value: string;
  children?: { value: string; children?: { value: string }[] }[];
}
