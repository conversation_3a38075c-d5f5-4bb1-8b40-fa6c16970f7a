/**
 * OAuth and integration constants
 */

export const OAUTH_URLS = {
  CLICKUP: {
    BASE_URL: "https://app.clickup.com/api",
    REDIRECT_URI: import.meta.env.VITE_API_CLICKUP_REDIRECT_URL
  },
} as const;

/**
 * Generate OAuth URL for ClickUp integration
 * @param encodedState - Encoded state parameter containing user data
 * @returns Complete OAuth URL for ClickUp
 */
export const generateClickUpOAuthUrl = (encodedState: string): string => {
  const clientId = import.meta.env.VITE_API_CLICKUP_CLIENTID;
  return `${OAUTH_URLS.CLICKUP.BASE_URL}?client_id=${clientId}&redirect_uri=${OAUTH_URLS.CLICKUP.REDIRECT_URI}?state=${encodedState}`;
};
