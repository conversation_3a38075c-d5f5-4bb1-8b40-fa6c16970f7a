import React from "react";
import { <PERSON>, <PERSON><PERSON>, Typo<PERSON>, IconButton } from "@mui/material";
import { format, addWeeks, subWeeks, isSameWeek, startOfWeek } from "date-fns";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import TodayIcon from "@mui/icons-material/Today";
import { WeekPickerProps } from "@/types/tracker";

const WeekPicker: React.FC<WeekPickerProps> = ({
  selectedDate,
  onWeekChange,
  weekStartsOn = 1, // Default to Monday
  showTodayButton = true,
}) => {
  const handlePreviousWeek = () => {
    onWeekChange(subWeeks(selectedDate, 1));
  };

  const handleNextWeek = () => {
    onWeekChange(addWeeks(selectedDate, 1));
  };

  const handleToday = () => {
    const today = new Date();
    const mondayOfCurrentWeek = startOfWeek(today, { weekStartsOn });
    onWeekChange(mondayOfCurrentWeek);
  };

  const formatWeekRange = (date: Date) => {
    const start = format(date, "MMM d");
    // Show the last day of the week (Sunday if weekStartsOn=1)
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 6); // Monday + 6 = Sunday
    const end = format(endDate, "MMM d, yyyy");
    return `${start} - ${end}`;
  };

  const isCurrentWeek = isSameWeek(selectedDate, new Date(), { weekStartsOn });

  return (
    <Box display="flex" alignItems="center" gap={2}>
      <IconButton onClick={handlePreviousWeek} size="small" aria-label="Previous week">
        <NavigateBeforeIcon />
      </IconButton>

      <Box display="flex" flexDirection="column" alignItems="center" minWidth="200px">
        <Typography variant="subtitle2" color="textSecondary">
          {format(selectedDate, "MMMM yyyy")}
        </Typography>
        <Typography variant="h6" component="div">
          {formatWeekRange(selectedDate)}
        </Typography>
      </Box>

      <IconButton onClick={handleNextWeek} size="small" aria-label="Next week" disabled={isCurrentWeek}>
        <NavigateNextIcon />
      </IconButton>

      {showTodayButton && (
        <Button
          variant="outlined"
          size="small"
          startIcon={<TodayIcon />}
          onClick={handleToday}
          disabled={isCurrentWeek}
          sx={{ ml: 1 }}
        >
          This Week
        </Button>
      )}
    </Box>
  );
};

export default React.memo(WeekPicker);
