import React from "react";
import { <PERSON>, <PERSON>, Avatar, Typo<PERSON>, Chip, Stack } from "@mui/material";

const statusColorMap = {
  active: "success",
  inactive: "default",
  pending: "warning",
} as const;

const ViewTeamMember: React.FC = () => {
  return (
    <Card
      elevation={3}
      sx={{
        p: 3,
        borderRadius: 4,
        maxWidth: 600,
        mx: "auto",
        mt: 4,
        backgroundColor: "background.paper",
        position: "relative",
      }}
    >
      {/* <Tooltip title="Edit User">
        <IconButton
          // onClick={handleEdit}
          sx={{ position: "absolute", top: 8, right: 8 }}
          color="primary"
        >
          <EditIcon />
        </IconButton>
      </Tooltip> */}

      <Stack direction="row" spacing={2} alignItems="center">
        <Avatar
          src={
            "https://trackify.outcodetest.com/api/uploads/image-1672113962128.jpeg"
          }
          alt={"Niroj Dyola"}
          sx={{ width: 72, height: 72 }}
        />

        <Box flex={1}>
          <Typography variant="h6">{"Niroj Dyola"}</Typography>
          <Typography variant="body2">{"<EMAIL>"}</Typography>
          <Typography variant="body2">{"+977 9812345678"}</Typography>

          <Chip
            label={"active"}
            color={statusColorMap["active"]}
            size="small"
            sx={{ mt: 1 }}
          />
        </Box>
      </Stack>

      {["Admin", "Member"].length > 0 && (
        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>
            Groups:
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {["Admin", "Member"].map((group) => (
              <Chip key={group} label={group} variant="outlined" />
            ))}
          </Stack>
        </Box>
      )}
    </Card>
  );
};

export default ViewTeamMember;
