import React, { Suspense } from "react";
import { createBrowser<PERSON>outer, Navigate, Outlet, RouterProvider } from "react-router-dom";
import PageNotFound from "@/screens/errors/not-found.screen";
import LoginPage from "@/screens/login/login.screen";
import ForgotPassword from "@/screens/login/forgot-password.screen";
import { useAppSelector } from "@/store/hooks/hooks";
import Tracker from "@/screens/dashboard/tracker.screen";
import ProjectsListScreen from "@/screens/project/project.list.screen";
import TeamsScreen from "@/screens/teams/teams.screen";
import ViewTeamMember from "@/screens/teams/view-team-screen";
import Home from "@/screens/home/<USER>";
import ProfileScreen from "@/screens/dashboard/profile.screen";
import ProjectViewScreen from "@/screens/project/project.view.screen";
import ProjectTypeScreen from "@/screens/project/project.types.screen";
import SetPassword from "@/screens/registeruser/userInviteRegister.screen";
import Clients<PERSON>istScreen from "@/screens/client/client.list.screen";
import CustomDashboardLayout from "@/layout/home-layout.layout";
import ResetPasswordConfirm from "@/screens/login/reset-password-confirm.screen";
import TeamsGroup from "@/screens/teams/teams-group.screen";
import { TeamMembers } from "@/screens/teams/team-members.screen";
import ViewGroupData from "@/screens/teams/view-group-data.screen";
import ResumeScreen from "@/screens/resume/resumeScreen";
import IntegrationScreen from "@/screens/dashboard/integration.screen";
import SettingsLayout from "@/screens/dashboard/setting.screen";
import ResumeEditScreen from "@/screens/resume/resumeEditScreen";
import ReportsScreen from "@/screens/reports/reports.screen";

// Redirect if token exists
const RedirectIfAuthenticated: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  if (!isAuthenticated && !user) {
    return <Navigate to="/login" replace />;
  }
  return <>{children || <Outlet />}</>;
};
const router = createBrowserRouter([
  {
    path: "/",
    element: (
      <RedirectIfAuthenticated>
        <CustomDashboardLayout />
      </RedirectIfAuthenticated>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/tracker" replace />,
      },
      {
        path: "tracker",
        element: <Tracker />,
      },
      {
        path: "dashboard",
        element: <Home />,
      },
      {
        path: "profile",
        element: <ProfileScreen />,
      },

      {
        path: "reports",
        element: <ReportsScreen />,
      },
      {
        path: "projects",
        element: <ProjectsListScreen />,
      },
      {
        path: "projects/:id",
        element: <ProjectViewScreen />,
      },
      {
        path: "projects/types",
        element: <ProjectTypeScreen />,
      },
      {
        path: "clients",
        element: <ClientsListScreen />,
      },
      {
        path: "teams",
        element: <TeamsScreen />,
      },
      {
        path: "teams/members",
        element: <TeamMembers />,
      },
      {
        path: "teams/groups",
        element: <TeamsGroup />,
      },
      {
        path: "teams/groups/view/:id",
        element: <ViewGroupData />,
      },
      {
        path: "teams-create",
        element: <ViewTeamMember />,
      },
      //  {
      //   path: "settings/public-profile/:id?",
      //   element: <ResumeScreen />,
      // },
      {
        // settings
        path: "settings",
        element: <SettingsLayout />,
        children: [
          {
            index: true,
            element: <Navigate to="integrations" replace />,
          },
          {
            path: "public-profile/:id?",
            element: <ResumeScreen />,
          },
          {
            path: "public-profile/edit/:id",
            element: <ResumeEditScreen />,
          },
          {
            path: "integrations",
            element: <IntegrationScreen />,
          },
        ],
      },
    ],
  },
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/forgot-password",
    element: <ForgotPassword />,
  },
  {
    path: "/password/reset/:uid/:token",
    element: <ResetPasswordConfirm />,
  },
  {
    path: "/register/:uid/:token/",
    element: <SetPassword />,
  },
  {
    path: "*",
    element: <PageNotFound />,
  },
  // {
  //   path: "/public-profile/:userId", // Added new route for public profile
  //   element: <PublicProfileScreen />,
  // },
]);
const Router: React.FC = () => {
  return (
    <Suspense fallback={"/"}>
      <RouterProvider router={router} />
    </Suspense>
  );
};

export default Router;
