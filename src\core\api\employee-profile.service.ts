import { IEmployeeSearchResult } from "./employee-search.service";
import Resource from "./resource";
import { IUserResume } from "./users.service";
import UsersService from "./users.service";
import {
  IEmployeeProfile,
  IProfileDetailNode,
  ICandidateBackground,
  IEducation,
  IWorkExperience,
  ILanguage,
} from "@/interfaces/employeeProfile.interface";

// Initialize the users resource
const usersResource = new UsersService.UsersResource();

class EmployeeProfileService extends Resource {
  constructor() {
    super("users");
  }

  // Get employee profile/resume by user ID
  static async getEmployeeProfile(userId: string): Promise<IUserResume> {
    const response = await usersResource.getUserResume(userId);
    return response.data;
  }

  // Update employee profile/resume
  static async updateEmployeeProfile(
    userId: string,
    profileData: IUserResume,
  ): Promise<IUserResume> {
    const response = await usersResource.updateUserResume(userId, profileData);
    return response.data;
  }

  // Create employee profile/resume
  static async createEmployeeProfile(
    profileData: IUserResume,
  ): Promise<IUserResume> {
    const response = await usersResource.updateUserResume(
      profileData.user_id,
      profileData,
    );
    return response.data;
  }

  // Convert backend resume data to frontend profile format
  static convertToProfile(
    resumeData: IUserResume,
    user: IEmployeeSearchResult,
  ): IEmployeeProfile {
    // Helper function to extract data from detail nodes
    const extractFromDetails = (
      details: IProfileDetailNode[],
      sectionName: string,
    ): IProfileDetailNode | undefined => {
      return details.find(
        (detail) => detail.value.toLowerCase() === sectionName.toLowerCase(),
      );
    };

    // Extract candidate background
    const backgroundSection = extractFromDetails(
      resumeData.details,
      "Background",
    );
    const candidateBackground: ICandidateBackground = {
      coreLanguages: [],
      coreFrameworks: [],
      additionalSkills: [],
    };

    if (backgroundSection) {
      const coreLanguagesNode = backgroundSection.children?.find(
        (child) => child.value.toLowerCase() === "core language",
      );
      const coreFrameworksNode = backgroundSection.children?.find(
        (child) => child.value.toLowerCase() === "core framework",
      );
      const additionalSkillsNode = backgroundSection.children?.find(
        (child) => child.value.toLowerCase() === "additional skills",
      );

      if (coreLanguagesNode) {
        candidateBackground.coreLanguages =
          coreLanguagesNode.children?.map((child) => child.value) || [];
      }
      if (coreFrameworksNode) {
        candidateBackground.coreFrameworks =
          coreFrameworksNode.children?.map((child) => child.value) || [];
      }
      if (additionalSkillsNode) {
        candidateBackground.additionalSkills =
          additionalSkillsNode.children?.map((child) => child.value) || [];
      }
    }

    // Extract languages
    const languagesSection =
      extractFromDetails(resumeData.details, "Languages") ||
      extractFromDetails(resumeData.details, "english assessment");
    const languages: ILanguage[] = [];

    if (languagesSection && languagesSection.children) {
      languagesSection.children.forEach((langNode) => {
        const proficiencyNode = langNode.children?.[0];
        languages.push({
          language: langNode.value,
          proficiency: proficiencyNode?.value || "Intermediate",
        });
      });
    }

    // Extract education
    const educationSection = extractFromDetails(
      resumeData.details,
      "Education",
    );
    const education: IEducation[] = [];

    if (educationSection && educationSection.children) {
      educationSection.children.forEach((eduNode) => {
        const eduData: IEducation = {
          institution: eduNode.value,
          degree: "",
          fieldOfStudy: "",
          graduationYear: "",
        };

        if (eduNode.children) {
          eduData.degree = eduNode.children[0]?.value || "";
          eduData.fieldOfStudy = eduNode.children[1]?.value || "";
          eduData.graduationYear = eduNode.children[2]?.value || "";
        }

        education.push(eduData);
      });
    }

    // Extract work experience
    const workExperienceSection = extractFromDetails(
      resumeData.details,
      "Work Experience",
    );
    const workExperience: IWorkExperience[] = [];

    if (workExperienceSection && workExperienceSection.children) {
      workExperienceSection.children.forEach((expNode) => {
        const [company, location] = expNode.value.split(", ");
        const expData: IWorkExperience = {
          company: company || "",
          location: location || "",
          role: "",
          startDate: "",
          endDate: "",
          isCurrent: false,
          responsibilities: "",
          technologies: [],
        };
        if (expNode.children) {
          expNode.children.forEach((child) => {
            switch (child.value.toLowerCase()) {
              case "role": {
                expData.role = child.children?.[0]?.value || "";
                break;
              }
              case "duration": {
                const duration = child.children?.[0]?.value || "";
                const [start, end] = duration.split(" - ");
                expData.startDate = start || "";
                expData.endDate = end === "Present" ? "" : end || "";
                expData.isCurrent = end === "Present";
                break;
              }
              case "description": {
                expData.responsibilities = child.children?.[0]?.value || "";
                break;
              }
              case "technologies": {
                expData.technologies =
                  child.children?.map((tech) => tech.value) || [];
                break;
              }
            }
          });
        }

        workExperience.push(expData);
      });
    }

    // Extract availability
    const availabilitySection = extractFromDetails(
      resumeData.details,
      "Availability",
    );
    const availability =
      availabilitySection?.children?.[0]?.value || "Immediate";

    return {
      id: `profile-${user.id}`,
      selectedEmployee: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        department: user.department,
        jobTitle: user.jobTitle,
        contactNumber: user.contactNumber,
        address: user.address,
      },
      startDate: new Date().toISOString(),
      status: "active",
      candidateBackground,
      availability,
      education,
      workExperience,
      languages,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }
}

export { EmployeeProfileService };
export default EmployeeProfileService;
