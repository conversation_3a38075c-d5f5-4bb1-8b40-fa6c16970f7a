import React from "react";
import { Box, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import { WeeklyTimelogList } from "@/components/tracker/historical-data";
import TrackerForm from "@/components/tracker/TrackerForm";
import { RootState } from "@/store/store";
import { FormValues } from "@/types/tracker";

const Tracker: React.FC = () => {
  const [trackerResetKey, setTrackerResetKey] = React.useState(0);
  const userId = useSelector((state: RootState) => state.auth.user?.id);
  const trackerFormRef = React.useRef<{ restartWithValues: (values: FormValues) => Promise<void> } | null>(null);

  const handleTrackerStateChange = React.useCallback((isTracking: boolean) => {
    if (!isTracking) {
      setTrackerResetKey(prev => prev + 1);
    }
  }, []);

  const handleRestartTimer = React.useCallback(async (values: FormValues) => {
    if (trackerFormRef.current) {
      await trackerFormRef.current.restartWithValues(values);
    }
  }, []);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 6, p: 3 }}>
      <Typography variant="h1">Tracker</Typography>

      <TrackerForm ref={trackerFormRef} onTrackerStateChange={handleTrackerStateChange} />

      <Box mt={4}>
        <WeeklyTimelogList key={trackerResetKey} userId={userId} onRestartTimer={handleRestartTimer} />
      </Box>
    </Box>
  );
};

export default Tracker;
