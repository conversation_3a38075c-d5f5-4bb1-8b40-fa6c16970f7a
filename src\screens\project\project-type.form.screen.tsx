import React from "react";
import { Box, Button, G<PERSON>, TextField, CircularProgress } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { ProjectType } from "@/interfaces/project.interface";

interface ProjectTypeFormProps {
  selectedProjectType: ProjectType | null;
  onSubmit: (data: { id?: string; name: string }) => void;
  isSubmitting: boolean;
  onClose: () => void;
}

const validationSchema = yup.object({
  name: yup.string().required("Name is required"),
});

const ProjectTypeForm: React.FC<ProjectTypeFormProps> = ({ selectedProjectType, onSubmit, isSubmitting }) => {
  const formik = useFormik({
    initialValues: {
      name: selectedProjectType?.name || "",
    },
    enableReinitialize: true,
    validationSchema,
    onSubmit: values => {
      if (selectedProjectType?.id) {
        onSubmit({ id: selectedProjectType.id, name: values.name });
      } else {
        onSubmit({ name: values.name });
      }
      formik.resetForm();
    },
  });

  return (
    <Box sx={{ mt: 1 }}>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid size={12}>
            <TextField
              fullWidth
              id="name"
              name="name"
              label="Name"
              value={formik.values.name}
              onChange={formik.handleChange}
              error={formik.touched.name && Boolean(formik.errors.name)}
              helperText={formik.touched.name && formik.errors.name}
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            />
          </Grid>
          <Grid size={12}>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
              sx={{ bgcolor: "rgb(0, 107, 168)" }}
              startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {selectedProjectType ? "Update" : "Create"}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default ProjectTypeForm;
