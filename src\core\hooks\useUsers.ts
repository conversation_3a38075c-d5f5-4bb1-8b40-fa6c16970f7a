import { useQuery } from "@tanstack/react-query";
import UsersService, { IUser, IUsersResponse } from "@/core/api/users.service";

const usersResource = new UsersService.UsersResource();

// Hook to get all active users
export const useUsers = () => {
  return useQuery<IUser[]>({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await usersResource.getActiveUsers();
      const usersData = response.data as IUsersResponse;
      return usersData.results;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
};
