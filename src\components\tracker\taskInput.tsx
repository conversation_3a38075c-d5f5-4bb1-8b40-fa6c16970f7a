import React, { useCallback, useMemo } from "react";
import { Autocomplete, TextField, CircularProgress, Typo<PERSON>, Box, Button, Alert, Chip } from "@mui/material";
import { useFormikContext } from "formik";
import { FormValues, TaskInputProps } from "@/types/tracker";

const TaskInput: React.FC<TaskInputProps> = ({
  inputOptions,
  name,
  label = "Projects",
  onInputChange,
  onSelectProject,
  disabled = false,
  hasNextPage = false,
  isFetchingNextPage = false,
  onLoadMore,
  error = false,
  errorMessage,
  onRetry,
  onFieldUpdate,
  specificTask,
}) => {
  const formik = useFormikContext<FormValues>();
  const { values, errors, touched, setFieldValue, setFieldTouched } = formik;

  // Handle scroll for infinite loading with optimized threshold
  const handleListboxScroll = useCallback(
    (event: React.SyntheticEvent) => {
      const listboxNode = event.currentTarget as HTMLElement;
      const { scrollTop, scrollHeight, clientHeight } = listboxNode;

      // Check if scrolled to bottom (with 20px threshold for better UX)
      if (scrollHeight - scrollTop <= clientHeight + 20) {
        if (hasNextPage && !isFetchingNextPage && onLoadMore) {
          onLoadMore();
        }
      }
    },
    [hasNextPage, isFetchingNextPage, onLoadMore]
  );

  // Memoize render option to prevent unnecessary re-renders
  const renderOption = useCallback(
    (props: React.HTMLAttributes<HTMLLIElement>, option: import("@/types/tracker").ProjectOption) => (
      <li {...props} key={option.value}>
        {option.isPending ? (
          <Box display="flex" alignItems="center" justifyContent="center" p={1}>
            <CircularProgress size={16} />
            <Typography variant="caption" sx={{ ml: 1 }}>
              {option.label}
            </Typography>
          </Box>
        ) : (
          <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
            <Typography>{option.label}</Typography>
            {option.status && (
              <Chip
                label={option.status.toUpperCase()}
                size="small"
                variant="filled"
                sx={{
                  ml: 1,
                  fontSize: "0.75rem",
                  height: "20px",
                  textTransform: "capitalize",
                }}
              />
            )}
          </Box>
        )}
      </li>
    ),
    []
  );

  // Memoize no options text to prevent unnecessary re-renders
  const noOptionsText = useMemo(() => {
    if (error) {
      return (
        <Box p={2}>
          <Alert severity="error" sx={{ mb: 1 }}>
            {errorMessage || "Failed to load tasks"}
          </Alert>
          {onRetry && (
            <Button size="small" variant="contained" onClick={onRetry}>
              Retry
            </Button>
          )}
        </Box>
      );
    }

    if (isFetchingNextPage) {
      return (
        <Box display="flex" alignItems="center" justifyContent="center" p={1}>
          <CircularProgress size={20} />
          <Typography sx={{ ml: 1 }}>Loading tasks...</Typography>
        </Box>
      );
    }

    return "No tasks found";
  }, [error, errorMessage, onRetry, isFetchingNextPage]);

  // Combine original options with loading indicator and handle missing current value
  const optionsWithLoading = React.useMemo(() => {
    const options = [...inputOptions];
    const currentValue = values[name as keyof FormValues] as string;

    // If current value exists but isn't in options, add it as a temporary option
    if (currentValue && currentValue !== "" && !options.find(option => option.value === currentValue)) {
      // Use specific task name if available, otherwise show task ID
      const taskLabel =
        specificTask && specificTask.task_id === currentValue ? specificTask.name : `Task ID: ${currentValue}`;

      options.unshift({
        label: taskLabel,
        value: currentValue,
      });
    }

    // Add a loading indicator as the last option when fetching more
    if (isFetchingNextPage && hasNextPage) {
      options.push({
        label: "Loading more tasks...",
        value: "__loading__",
        isPending: true,
      });
    }

    return options;
  }, [inputOptions, isFetchingNextPage, hasNextPage, values, name, specificTask]);

  return (
    <Autocomplete
      options={optionsWithLoading}
      getOptionLabel={option => option.label}
      disabled={disabled}
      onInputChange={(_event, value) => {
        onInputChange(value);
      }}
      onChange={(_event, newValue) => {
        // Don't select the loading option
        if (newValue?.value === "__loading__") return;

        setFieldValue(name, newValue?.value || "");
        if (onSelectProject) {
          onSelectProject(newValue);
        }

        // Trigger field update if callback is provided
        if (onFieldUpdate) {
          onFieldUpdate(name, newValue?.value || "");
        }
      }}
      value={
        optionsWithLoading.find(
          option => option.value === values[name as keyof FormValues] && option.value !== "__loading__"
        ) || null
      }
      ListboxProps={{
        onScroll: handleListboxScroll,
        style: { maxHeight: 300 }, // Limit height to enable scrolling
      }}
      renderOption={renderOption}
      // Add loading spinner at the end of the options list
      renderGroup={params => (
        <div key={params.key}>
          {params.children}
          {isFetchingNextPage && hasNextPage && (
            <Box display="flex" alignItems="center" justifyContent="center" p={1}>
              <CircularProgress size={16} />
              <Typography variant="caption" sx={{ ml: 1 }}>
                Loading more tasks...
              </Typography>
            </Box>
          )}
        </div>
      )}
      renderInput={params => (
        <TextField
          {...params}
          label={label}
          name={name}
          error={touched[name as keyof FormValues] && Boolean(errors[name as keyof FormValues])}
          aria-invalid={touched[name as keyof FormValues] && Boolean(errors[name as keyof FormValues])}
          aria-describedby={
            touched[name as keyof FormValues] && Boolean(errors[name as keyof FormValues]) ? `${name}-error` : undefined
          }
          sx={{
            "& .MuiInputLabel-root": {
              color: "text.primary",
            },
            "& .MuiInputLabel-root.Mui-focused": {
              color: "text.primary",
            },
            "& .MuiOutlinedInput-root": {
              boxSizing: "border-box",
            },
          }}
          onBlur={() => setFieldTouched(name, true)}
        />
      )}
      // Add loading indicator at the bottom
      noOptionsText={noOptionsText}
      // Show loading state in the input field
      loading={isFetchingNextPage && inputOptions.length === 0}
      sx={{
        width: "100%",
        minWidth: 200,
        "& .MuiFormControl-root": {
          boxSizing: "border-box",
        },
      }}
      disableClearable={false}
    />
  );
};

export default TaskInput;
