import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  Autocomplete,
  TextField,
  CircularProgress,
  Typography,
  Box,
  Button,
  Alert,
  Chip,
  debounce,
} from "@mui/material";

import { AutocompleteOption, AutocompleteInfiniteProps } from "@/types/reports";

const AutocompleteInfinite = <T extends AutocompleteOption>({
  value,
  onChange,
  useInfiniteOptions,
  label = "Select",
  placeholder = "Search...",
  multiple = true,
  disabled = false,
  error = false,
  helperText,
}: AutocompleteInfiniteProps<T>) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [selectedOptionsCache, setSelectedOptionsCache] = useState<Map<string, T>>(new Map());

  const { options, hasNextPage, fetchNextPage, isFetchingNextPage, isLoading, isError, retry } =
    useInfiniteOptions(searchTerm);

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchTerm(value);
      }, 300),
    []
  );

  const handleListboxScroll = useCallback(
    (event: React.SyntheticEvent) => {
      const listboxNode = event.currentTarget as HTMLElement;
      const { scrollTop, scrollHeight, clientHeight } = listboxNode;

      if (scrollHeight - scrollTop <= clientHeight + 20) {
        if (hasNextPage && !isFetchingNextPage) {
          fetchNextPage();
        }
      }
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage]
  );

  const renderOption = useCallback(
    (props: React.HTMLAttributes<HTMLLIElement>, option: T) => (
      <li {...props} key={option.value}>
        {option.isPending ? (
          <Box display="flex" alignItems="center" justifyContent="center" p={1}>
            <CircularProgress size={16} />
            <Typography variant="caption" sx={{ ml: 1 }}>
              {option.label}
            </Typography>
          </Box>
        ) : (
          <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
            <Typography>{option.label}</Typography>
          </Box>
        )}
      </li>
    ),
    []
  );

  const noOptionsText = useMemo(() => {
    if (isError) {
      return (
        <Box p={2}>
          <Alert severity="error" sx={{ mb: 1 }}>
            Failed to load items
          </Alert>
          <Button size="small" variant="contained" onClick={() => retry()}>
            Retry
          </Button>
        </Box>
      );
    }

    if (isLoading) {
      return (
        <Box display="flex" alignItems="center" justifyContent="center" p={1}>
          <CircularProgress size={20} />
          <Typography sx={{ ml: 1 }}>Loading items...</Typography>
        </Box>
      );
    }

    return "No items found";
  }, [isError, isLoading, retry]);

  const optionsWithLoading = useMemo(() => {
    const currentOptions = [...options];

    if (isFetchingNextPage && hasNextPage) {
      currentOptions.push({
        label: "Loading more items...",
        value: "__loading__",
        isPending: true,
      } as T & { isPending: true });
    }

    return currentOptions;
  }, [options, isFetchingNextPage, hasNextPage]);

  useEffect(() => {
    options.forEach(option => {
      if (option.value !== "__loading__") {
        setSelectedOptionsCache(prev => {
          const newCache = new Map(prev);
          newCache.set(option.value, option);
          return newCache;
        });
      }
    });
  }, [options]);

  const selectedOptions = useMemo(() => {
    if (!value || value.length === 0) return multiple ? [] : null;

    const resolvedOptions = value.map(val => {
      const cachedOption = selectedOptionsCache.get(val);
      if (cachedOption) return cachedOption;

      const existingOption = options.find(option => option.value === val);
      if (existingOption) return existingOption;

      return {
        label: `Item ${val}`,
        value: val,
        isPending: false,
      } as T;
    });

    return multiple ? resolvedOptions : resolvedOptions[0] || null;
  }, [value, options, multiple, selectedOptionsCache]);

  return (
    <Autocomplete
      multiple={multiple}
      options={optionsWithLoading}
      getOptionLabel={option => option.label}
      disabled={disabled}
      loading={isLoading && options.length === 0}
      inputValue={inputValue}
      freeSolo={false}
      clearOnBlur={false}
      clearOnEscape={true}
      blurOnSelect={false}
      onInputChange={(_event, newInputValue, reason) => {
        if (reason === "input" || reason === "clear") {
          setInputValue(newInputValue);
          debouncedSearch(newInputValue);
        }
        if (reason === "reset" && multiple) {
          setInputValue("");
        }
      }}
      onChange={(_event, newValue) => {
        if (multiple) {
          const validOptions = (newValue as T[]).filter(option => option.value !== "__loading__");
          validOptions.forEach(option => {
            setSelectedOptionsCache(prev => {
              const newCache = new Map(prev);
              newCache.set(option.value, option);
              return newCache;
            });
          });
          onChange(validOptions.map(option => option.value));
          setInputValue("");
        } else {
          const selectedOption = newValue as T | null;
          if (selectedOption && selectedOption.value !== "__loading__") {
            setSelectedOptionsCache(prev => {
              const newCache = new Map(prev);
              newCache.set(selectedOption.value, selectedOption);
              return newCache;
            });
            onChange([selectedOption.value]);
          } else {
            onChange([]);
          }
          setInputValue("");
        }
      }}
      value={selectedOptions}
      ListboxProps={{
        onScroll: handleListboxScroll,
        style: { maxHeight: 300 },
      }}
      renderOption={renderOption}
      renderTags={(tagValue, getTagProps) => {
        const maxVisibleTags = 1;
        const visibleTags = tagValue.slice(0, maxVisibleTags);
        const hiddenCount = tagValue.length - maxVisibleTags;

        return (
          <React.Fragment>
            {visibleTags.map((option, index) => {
              const optionObj =
                typeof option === "string"
                  ? (Array.isArray(selectedOptions) ? selectedOptions : [selectedOptions]).find(
                      (sel: T | null) => sel?.value === option
                    ) || { label: option, value: option }
                  : option;

              return (
                <Chip
                  {...getTagProps({ index })}
                  key={optionObj.value}
                  label={optionObj.label}
                  size="small"
                  variant="outlined"
                />
              );
            })}
            {hiddenCount > 0 && (
              <Chip
                label={`+${hiddenCount}`}
                size="small"
                variant="outlined"
                color="primary"
                sx={{
                  backgroundColor: "primary.light",
                  color: "primary.contrastText",
                  fontWeight: "bold",
                }}
              />
            )}
          </React.Fragment>
        );
      }}
      renderInput={params => (
        <TextField
          {...params}
          label={label}
          placeholder={inputValue || (value && value.length > 0) ? "" : placeholder}
          error={error}
          helperText={helperText}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <React.Fragment>
                {isLoading && options.length === 0 ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </React.Fragment>
            ),
          }}
        />
      )}
      noOptionsText={noOptionsText}
      sx={{ width: "100%" }}
      filterOptions={options => {
        return options;
      }}
    />
  );
};

export default AutocompleteInfinite;
