import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Box, Typography, Paper } from "@mui/material";
import { useEffect, useState } from "react";
import ToastMessage from "@/components/toast/toast.component";
import EmployeeProfileForm from "@/screens/profile-sharing/employee.profile.form";
import UsersService from "@/core/api/users.service";
import EmployeeProfileService from "@/core/api/employee-profile.service";
import { IEmployeeProfile, IEmployeeProfilePayload } from "@/interfaces/employeeProfile.interface";
import { useEmployeeProfileMutation } from "@/core/hooks/useEmployeeProfile";

const ResumeEditScreen = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [profile, setProfile] = useState<IEmployeeProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const { mutateAsync: saveEmployeeProfile } = useEmployeeProfileMutation();

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const usersResource = new UsersService.UsersResource();
        const user = await usersResource.get(id!);
        const resume = await usersResource.getUserResume(id!);

        const profile = EmployeeProfileService.convertToProfile(resume.data, {
          id: user.data.id,
          firstName: user.data.first_name,
          lastName: user.data.last_name,
          email: user.data.email,
          department: user.data.groups?.[0]?.name || "Unknown",
          jobTitle: "Team Member",
          contactNumber: "",
          address: "",
        });

        setProfile(profile);
      } catch {
        ToastMessage.error("Failed to load profile");
      } finally {
        setLoading(false);
      }
    };

    if (id) fetchProfile();
  }, [id]);

  const handleSave = async (profileData: IEmployeeProfilePayload) => {
    try {
      await saveEmployeeProfile({
        profileData,
        isEditing: profile !== null, // or use `profile` state from your ResumeEditScreen
        userId: profile?.selectedEmployee?.id || "",
      });

      ToastMessage.success("Employee profile saved successfully!");
      // If you want, navigate back after save:
      // navigate(`/settings/public-profile/${id}`);
    } catch (error) {
      ToastMessage.error("Failed to save employee profile. Please try again.");
      throw error; // important to re-throw so form knows about failure
    }
  };

  if (loading) {
    return <Typography sx={{ p: 3 }}>Loading...</Typography>;
  }

  if (!profile) {
    return (
      <Typography sx={{ p: 3 }} color="error">
        Profile not found.
      </Typography>
    );
  }

  return (
    <Box p={2}>
      <Typography variant="h5" mb={2}>
        Edit Profile
      </Typography>
      <Paper elevation={1} sx={{ p: 2 }}>
        <EmployeeProfileForm
          onClose={() => navigate(-1)}
          onSave={handleSave}
          employeeProfile={profile}
          selectedEmployeeId={profile?.selectedEmployee.id}
          modal={false} // IMPORTANT: no modal dialog, full page inline form
        />
      </Paper>
    </Box>
  );
};

export default ResumeEditScreen;
