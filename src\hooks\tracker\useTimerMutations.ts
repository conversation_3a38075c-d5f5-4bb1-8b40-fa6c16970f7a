import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { FormValues, TimelogData, TimelogsQueryData } from "@/types/tracker";
import ToastMessage from "@/components/toast/toast.component";
import { getErrorMessage } from "@/utils/api-error.utils";

export const useTimerMutations = (
  userId: string | undefined,
  onStartSuccess?: (data: TimelogData) => void,
  onStopSuccess?: (resetForm?: () => void) => void,
  onUpdateSuccess?: (data: TimelogData) => void,
  onUpdateError?: (error: unknown) => void
) => {
  const timelogResource = useMemo(() => new Resource("timelogs"), []);
  const queryClient = useQueryClient();

  const startTimerMutation = useMutation({
    mutationFn: (values: FormValues) => {
      const timelogData = {
        user: userId,
        project: values.project,
        description: values.description,
        clickup_task_id: values.clickupTaskName || null,
        is_billable: values.isBillable,
        is_ot: values.isOT,
      };
      return timelogResource.store(timelogData);
    },
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: ["timelogs", userId] });
      queryClient.invalidateQueries({ queryKey: ["weeklyTimelogs"] });
      onStartSuccess?.(response.data);
    },
    onError: error => {
      const errorMessage = getErrorMessage(error, "Failed to start timer. Please try again.");
      ToastMessage.error(errorMessage);
    },
  });
  const stopTimerMutation = useMutation({
    mutationFn: (variables: { timelogId: string; timelogData: TimelogData; resetForm?: () => void }) => {
      return timelogResource.patch(variables.timelogId, {
        end_time: new Date().toISOString(),
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["timelogs", userId] });
      queryClient.invalidateQueries({ queryKey: ["weeklyTimelogs"] });
      onStopSuccess?.(variables.resetForm);
    },
    onError: error => {
      const errorMessage = getErrorMessage(error, "Failed to stop timer. Please try again.");
      ToastMessage.error(errorMessage);
    },
  });
  const updateTimelogMutation = useMutation({
    mutationFn: (variables: {
      timelogId: string;
      updateData: {
        project?: string;
        clickup_task_id?: string;
        description?: string;
        is_billable?: boolean;
        is_ot?: boolean;
      };
    }) => {
      return timelogResource.patch(variables.timelogId, variables.updateData);
    },
    onMutate: async variables => {
      await queryClient.cancelQueries({ queryKey: ["timelogs", userId] });
      const previousTimelogs = queryClient.getQueryData(["timelogs", userId]);

      queryClient.setQueryData(["timelogs", userId], (old: unknown) => {
        if (!old || typeof old !== "object" || old === null) return old;
        if (!("data" in old) || !Array.isArray((old as TimelogsQueryData).data)) return old;

        const timelogData = old as TimelogsQueryData;
        return {
          ...timelogData,
          data: timelogData.data.map((timelog: TimelogData) => {
            if (timelog.id === variables.timelogId) {
              return {
                ...timelog,
                ...variables.updateData,
              };
            }
            return timelog;
          }),
        };
      });

      return { previousTimelogs };
    },
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: ["timelogs", userId] });
      onUpdateSuccess?.(response.data);
    },
    onError: (error, _variables, context) => {
      if (context?.previousTimelogs) {
        queryClient.setQueryData(["timelogs", userId], context.previousTimelogs);
      }
      onUpdateError?.(error);
    },

    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["timelogs", userId] });
      queryClient.invalidateQueries({ queryKey: ["weeklyTimelogs"] });
    },
  });

  return {
    startTimerMutation,
    stopTimerMutation,
    updateTimelogMutation,
  };
};
