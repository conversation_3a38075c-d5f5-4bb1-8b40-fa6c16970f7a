import React from "react";
import { FormControl, InputLabel, Select, MenuItem } from "@mui/material";
import { DetailNode } from "@/utils/employee-profile-form-utils";
import { availabilityOptions } from "./constants";

interface AvailabilitySectionProps {
  sectionIndex: number;
  section: DetailNode;
  setFieldValue: (field: string, value: unknown) => void;
}

const AvailabilitySection: React.FC<AvailabilitySectionProps> = ({
  sectionIndex,
  section,
  setFieldValue,
}) => {
  const handleAvailabilityChange = (value: string) => {
    setFieldValue(`details[${sectionIndex}].children`, [{ value }]);
  };

  return (
    <FormControl fullWidth>
      <InputLabel>Availability</InputLabel>
      <Select
        value={section.children?.[0]?.value || ""}
        onChange={(e) => handleAvailabilityChange(e.target.value as string)}
        label="Availability"
      >
        {availabilityOptions.map((option) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default AvailabilitySection;
