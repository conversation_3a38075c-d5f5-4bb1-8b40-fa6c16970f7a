import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  Box,
  Avatar,
  Chip,
  Divider,
} from "@mui/material";
import { IEmployeeProfile, IEducation, IWorkExperience } from "@/interfaces/employeeProfile.interface";
import { format } from "date-fns";
import DOMPurify from "dompurify";

interface EmployeeProfileViewModalProps {
  open: boolean;
  onClose: () => void;
  employeeProfile: IEmployeeProfile | null;
}

const DetailItem: React.FC<{
  label: string;
  value?: string | string[] | null;
  fullWidth?: boolean;
}> = ({ label, value, fullWidth = false }) => (
  <Grid size={{ xs: 12, sm: fullWidth ? 12 : 6 }}>
    <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ fontWeight: "bold" }}>
      {label}
    </Typography>
    {Array.isArray(value) ? (
      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
        {value.map((item, index) => (
          <Chip key={index} label={item} size="small" />
        ))}
      </Box>
    ) : (
      <Typography variant="body1" gutterBottom sx={{ whiteSpace: "pre-line" }}>
        {value || ""}
      </Typography>
    )}
  </Grid>
);

const SectionTitle: React.FC<{ title: string }> = ({ title }) => (
  <Grid size={12} sx={{ mt: 1 }}>
    <Typography variant="h6" gutterBottom component="div" sx={{ fontWeight: "medium" }}>
      {title}
    </Typography>
    <Divider sx={{ mb: 1 }} />
  </Grid>
);

const EmployeeProfileViewModal: React.FC<EmployeeProfileViewModalProps> = ({ open, onClose, employeeProfile }) => {
  if (!employeeProfile) return null;

  const { selectedEmployee, candidateBackground, languages, availability, education, workExperience } = employeeProfile;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      {" "}
      {}
      <DialogTitle sx={{ display: "flex", alignItems: "center", pb: 1 }}>
        <Avatar sx={{ width: 64, height: 64, mr: 2 }}>
          {" "}
          {}
          {selectedEmployee.firstName?.[0]}
          {selectedEmployee.lastName?.[0]}
        </Avatar>
        <Box>
          <Typography variant="h4">
            {selectedEmployee.firstName} {selectedEmployee.lastName}
          </Typography>{" "}
          {}
          <Typography variant="h6" color="text.secondary">
            {selectedEmployee.jobTitle}
          </Typography>{" "}
          {}
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={2} sx={{ pt: 1 }}>
          {}
          <DetailItem label="Email" value={selectedEmployee.email} />
          <DetailItem label="Department" value={selectedEmployee.department} />
          <DetailItem label="Contact Number" value={selectedEmployee.contactNumber} />
          <DetailItem label="Address" value={selectedEmployee.address} fullWidth />

          {}
          {candidateBackground && (
            <>
              <SectionTitle title="Candidate Background" />
              <DetailItem label="Core Languages" value={candidateBackground.coreLanguages} />
              <DetailItem label="Core Frameworks" value={candidateBackground.coreFrameworks} />
              <DetailItem label="Additional Skills" value={candidateBackground.additionalSkills} fullWidth />
            </>
          )}

          {}
          {languages && languages.length > 0 && (
            <>
              <SectionTitle title="Languages" />
              {languages.map((lang, index) => (
                <DetailItem key={index} label={lang.language} value={lang.proficiency} />
              ))}
            </>
          )}

          {}
          {availability && (
            <>
              <SectionTitle title="Availability" />
              <DetailItem label="Availability" value={availability} />
            </>
          )}

          {}
          {education && education.length > 0 && (
            <>
              <SectionTitle title="Education" />
              {education.map((edu: IEducation, index: number) => (
                <Grid size={12} key={`edu-${index}`} sx={{ mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
                    {edu.institution}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {edu.degree}
                    {edu.fieldOfStudy ? `, ${edu.fieldOfStudy}` : ""}{" "}
                    {edu.graduationYear ? `(${edu.graduationYear})` : ""}
                  </Typography>
                </Grid>
              ))}
            </>
          )}

          {}
          {workExperience && workExperience.length > 0 && (
            <>
              <SectionTitle title="Work Experience" />
              {workExperience.map((exp: IWorkExperience, index: number) => (
                <Grid size={12} key={`work-${index}`} sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
                    {exp.company}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {exp.role} |{exp.startDate ? format(new Date(exp.startDate), "MMM yyyy") : ""}-
                    {exp.isCurrent ? "Present" : exp.endDate ? format(new Date(exp.endDate), "MMM yyyy") : ""}
                  </Typography>
                  {exp.responsibilities && (
                    <Box sx={{ pl: 2, mt: 1 }}>
                      <Typography
                        variant="body2"
                        dangerouslySetInnerHTML={{
                          __html: DOMPurify.sanitize(exp.responsibilities),
                        }}
                      />
                    </Box>
                  )}
                  {exp.technologies && exp.technologies.length > 0 && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                        Technologies:{" "}
                      </Typography>
                      <Typography variant="caption">{exp.technologies.join(", ")}</Typography>
                    </Box>
                  )}
                  {index < workExperience.length - 1 && <Divider sx={{ mt: 2 }} />}
                </Grid>
              ))}
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions sx={{ p: "16px 24px" }}>
        <Button onClick={onClose} color="primary" variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmployeeProfileViewModal;
