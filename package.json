{"name": "free-time", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky", "test": "vitest", "test:ui": "vitest --ui", "test:report": "vitest run --coverage && open coverage/index.html", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\""}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.8.0", "@mui/x-date-pickers": "^8.8.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tinymce/miniature": "^6.0.0", "@tinymce/tinymce-react": "^6.2.1", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@toolpad/core": "^0.16.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "formik": "^2.4.6", "husky": "^9.1.7", "lint-staged": "^16.1.2", "mui-tel-input": "^9.0.1", "react": "^19.1.0", "react-cookie-consent": "^9.0.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "react-toastify": "^11.0.5", "recharts": "^3.1.0", "redux-persist": "^6.0.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/dompurify": "^3.2.0", "@types/lodash": "^4.17.20", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-istanbul": "^3.2.4", "@vitest/ui": "3.2.4", "eslint": "^9.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.37.0", "vite": "^7.0.5", "vitest": "^3.2.4"}, "overrides": {"brace-expansion": "^2.0.2", "minimatch": {"brace-expansion": "^2.0.2"}}}