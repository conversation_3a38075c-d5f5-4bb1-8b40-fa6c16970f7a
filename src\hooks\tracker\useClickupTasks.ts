import { useInfiniteQuery, keepPreviousData } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { ProjectOption, TaskData, ClickUpTasksResponse } from "@/types/tracker";

export const useClickupTasks = (
  spaceId: string | undefined | null,
  searchTerm: string,
  pageSize: number = 10 // Default to 10 as requested
) => {
  const clickupTaskResource = useMemo(() => new Resource("integrations/clickup-tasks"), []);

  // Normalize parameters to improve cache hit rates
  const normalizedSearchTerm = searchTerm.trim().toLowerCase();
  const normalizedPageSize = pageSize || 10;

  // Prevent API calls with empty or very short search terms unless there's a space_id
  const shouldFetch = useMemo(() => {
    return !!spaceId && (normalizedSearchTerm.length >= 2 || normalizedSearchTerm.length === 0);
  }, [spaceId, normalizedSearchTerm]);

  const query = useInfiniteQuery({
    queryKey: ["clickupTask", spaceId, normalizedSearchTerm, normalizedPageSize],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await clickupTaskResource.list({
        space_id: spaceId,
        search: normalizedSearchTerm,
        page: pageParam,
        page_size: normalizedPageSize,
      });
      return response.data;
    },
    enabled: shouldFetch,
    placeholderData: keepPreviousData,
    retry: 3, // Retry failed requests up to 3 times
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    staleTime: 10 * 60 * 1000, // 10 minutes - increased for better cache reuse
    gcTime: 30 * 60 * 1000, // 30 minutes - increased for better cache reuse
    refetchOnWindowFocus: false, // Don't refetch on window focus for better performance
    getNextPageParam: (lastPage: ClickUpTasksResponse) => {
      // Extract page number from next URL
      if (lastPage.next) {
        const url = new URL(lastPage.next);
        return parseInt(url.searchParams.get("page") || "1");
      }
      return undefined;
    },
    initialPageParam: 1,
  });

  // Flatten all pages into single array for dropdown
  const taskOptions: ProjectOption[] = useMemo(
    () =>
      query.data?.pages.flatMap(
        page =>
          page.results?.map((task: TaskData) => ({
            label: task.name,
            value: task.task_id,
            status: task.status,
          })) || []
      ) || [],
    [query.data?.pages]
  );

  return {
    ...query,
    taskOptions,
    hasNextPage: query.hasNextPage,
    fetchNextPage: query.fetchNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    totalCount: query.data?.pages[0]?.count || 0,
    error: query.error,
    isError: query.isError,
    retry: query.refetch,
  };
};
