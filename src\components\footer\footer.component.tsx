import React, { useState } from "react";
import { BottomNavigation, BottomNavigationAction, Box } from "@mui/material";
import HomeLogoImg from "@/assets/images/ic_home.png";
import MoreLogoImg from "@/assets/images/ic_more.png";
import { useNavigate } from "react-router-dom";

const Footer: React.FC<{ activeTab?: string }> = ({ activeTab }) => {
  const [activeLabel, setActiveLabel] = useState<string>(activeTab || "");
  const menuList = [
    { label: "Home", value: "home", icon: HomeLogoImg },
    { label: "More", value: "more", icon: MoreLogoImg },
  ];
  const location = window.location.pathname;
  const currentPage = location.split("/").pop() || "";
  if (currentPage && !activeLabel) {
    const matchedLabel = menuList.find(
      (item) => item.label.toLowerCase() === currentPage.toLowerCase()
    );
    setActiveLabel(matchedLabel ? currentPage : "more");
  }
  const navigate = useNavigate();
  return (
    <BottomNavigation
      showLabels
      value={activeLabel}
      sx={{
        position: "fixed",
        bottom: 0,
        left: 0,
        right: 0,
        bgcolor: "custom.brand.milk",
        zIndex: 1000,
      }}
    >
      {menuList.map(({ label, value, icon }) => (
        <BottomNavigationAction
          key={value}
          label={label}
          value={value}
          sx={{
            color: "custom.brand.charcoal",
            bgcolor:
              activeLabel === value ? "custom.brand.sand" : "transparent",
            borderRadius: "10px",
            transition: "background-color 0.3s ease-in-out",
          }}
          onClick={() => {
            setActiveLabel(value);
            navigate(`/${value}`);
          }}
          icon={<Box component="img" alt={label} src={icon} />}
        />
      ))}
    </BottomNavigation>
  );
};

export default Footer;
