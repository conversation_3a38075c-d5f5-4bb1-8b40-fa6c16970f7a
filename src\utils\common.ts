import { IStateData } from "@/interfaces/profile.interface";

export function encodeState(data: IStateData) {
  const jsonString = JSON.stringify(data);
  const base64String = btoa(jsonString);
  return encodeURIComponent(base64String);
}

export function truncateString(str: string, count: number = 20) {
  if (str) return str.slice(0, count) + (str.length > count ? "..." : "");
  return str;
}

export const formatTrackerTime = (timer: number) => {
  const getSeconds = `0${timer % 60}`.slice(-2);
  const minutes = `${Math.floor(timer / 60)}`;
  const getMinutes = `0${Number(minutes) % 60}`.slice(-2);
  const getHours = `0${Math.floor(timer / 3600)}`.slice(-2);

  return `${getHours} : ${getMinutes} : ${getSeconds}`;
};
