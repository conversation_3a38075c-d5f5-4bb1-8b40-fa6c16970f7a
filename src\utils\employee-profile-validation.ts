import * as Yup from "yup";
import { FormValues } from "./employee-profile-form-utils";

/**
 * Enhanced validation schema for Employee Profile Form
 * Provides comprehensive validation for all sections and fields
 */

// Define interfaces for validation data
interface ValidationChild {
  value: string;
  children?: ValidationChild[];
}

interface ValidationItem {
  value: string;
  children?: ValidationChild[];
}

// Simplified validation schema that can be extended with custom validation functions
export const employeeProfileValidationSchema = Yup.object({
  user_id: Yup.string()
    .required("Employee selection is required")
    .min(1, "Please select an employee"),

  details: Yup.array()
    .of(
      Yup.object({
        value: Yup.string().required(),
        children: Yup.array().optional(),
      }),
    )
    .required("Profile sections are required"),
});

// Specific validation functions for each section
export const validateWorkExperienceSection = (
  workExperience: ValidationItem[],
): string[] => {
  const errors: string[] = [];

  workExperience.forEach((work, index) => {
    if (!work.value || work.value.trim().length < 2) {
      errors.push(
        `Work Experience ${index + 1}: Company name is required and must be at least 2 characters`,
      );
    }

    if (work.value && work.value.length > 100) {
      errors.push(
        `Work Experience ${index + 1}: Company name must be less than 100 characters`,
      );
    }

    const roleChild = work.children?.find(
      (c: ValidationChild) => c.value === "role",
    );
    const role = roleChild?.children?.[0]?.value;
    if (!role || role.trim().length < 2) {
      errors.push(
        `Work Experience ${index + 1}: Role is required and must be at least 2 characters`,
      );
    }

    const durationChild = work.children?.find(
      (c: ValidationChild) => c.value === "duration",
    );
    const duration = durationChild?.children?.[0]?.value;
    if (!duration) {
      errors.push(`Work Experience ${index + 1}: Duration is required`);
    } else {
      // Validate duration format and logic
      const parts = duration.split(" - ");
      if (parts.length !== 2) {
        errors.push(`Work Experience ${index + 1}: Invalid duration format`);
      } else {
        const startDate = parts[0];
        const endDate = parts[1];

        if (!startDate || !endDate) {
          errors.push(`Work Experience ${index + 1}: Invalid duration format`);
          return;
        }

        const isCurrent = endDate === "Present";
        // Parse dates from formatted strings (e.g., "Jun 2025" -> Date object)
        const parseFormattedDate = (dateStr: string): Date | null => {
          try {
            // Handle formatted dates like "Jun 2025"
            const date = new Date(dateStr + " 01");
            return isNaN(date.getTime()) ? null : date;
          } catch {
            return null;
          }
        };

        const startDateObj = parseFormattedDate(startDate);
        const endDateObj = isCurrent ? null : parseFormattedDate(endDate);

        if (!startDateObj) {
          errors.push(
            `Work Experience ${index + 1}: Invalid start date format`,
          );
        }

        if (!isCurrent && !endDateObj) {
          errors.push(`Work Experience ${index + 1}: Invalid end date format`);
        }

        // Check if dates are not in the future
        const today = new Date();
        if (startDateObj && startDateObj > today) {
          errors.push(
            `Work Experience ${index + 1}: Start date cannot be in the future`,
          );
        }

        if (!isCurrent && endDateObj) {
          if (endDateObj > today) {
            errors.push(
              `Work Experience ${index + 1}: End date cannot be in the future`,
            );
          }
          if (startDateObj && endDateObj < startDateObj) {
            errors.push(
              `Work Experience ${index + 1}: End date must be after start date`,
            );
          }
        }
      }
    }

    const descriptionChild = work.children?.find(
      (c: ValidationChild) => c.value === "description",
    );
    const description = descriptionChild?.children?.[0]?.value;
    if (!description || description.trim().length < 10) {
      errors.push(
        `Work Experience ${index + 1}: Description is required and must be at least 10 characters`,
      );
    }

    if (description && description.length > 2000) {
      errors.push(
        `Work Experience ${index + 1}: Description must be less than 2000 characters`,
      );
    }
  });

  return errors;
};

export const validateEducationSection = (
  education: ValidationItem[],
): string[] => {
  const errors: string[] = [];
  const currentYear = new Date().getFullYear();

  education.forEach((edu, index) => {
    if (!edu.value || edu.value.trim().length < 2) {
      errors.push(
        `Education ${index + 1}: Institution name is required and must be at least 2 characters`,
      );
    }

    if (edu.value && edu.value.length > 100) {
      errors.push(
        `Education ${index + 1}: Institution name must be less than 100 characters`,
      );
    }

    const degree = edu.children?.[0]?.value;
    if (!degree || degree.trim().length < 2) {
      errors.push(
        `Education ${index + 1}: Degree is required and must be at least 2 characters`,
      );
    }

    if (degree && degree.length > 100) {
      errors.push(
        `Education ${index + 1}: Degree must be less than 100 characters`,
      );
    }

    // Optional graduation year validation
    const graduationYear = edu.children?.[2]?.value;
    if (graduationYear) {
      const year = parseInt(graduationYear);
      if (isNaN(year) || year < 1950 || year > currentYear + 6) {
        errors.push(
          `Education ${index + 1}: Graduation year must be between 1950 and ${currentYear + 6}`,
        );
      }
    }
  });

  return errors;
};

export const validateLanguagesSection = (
  languages: ValidationItem[],
): string[] => {
  const errors: string[] = [];
  const validLanguages = [
    "English",
    "Spanish",
    "French",
    "German",
    "Italian",
    "Portuguese",
    "Dutch",
    "Russian",
    "Chinese (Mandarin)",
    "Chinese (Cantonese)",
    "Japanese",
    "Korean",
    "Arabic",
    "Hindi",
    "Bengali",
    "Punjabi",
    "Tamil",
    "Telugu",
    "Marathi",
    "Gujarati",
    "Urdu",
    "Turkish",
    "Polish",
    "Romanian",
    "Czech",
    "Hungarian",
    "Swedish",
    "Norwegian",
    "Danish",
    "Finnish",
    "Greek",
    "Hebrew",
    "Thai",
    "Vietnamese",
    "Indonesian",
    "Malay",
    "Tagalog",
    "Swahili",
  ];
  const validProficiencies = ["Beginner", "Intermediate", "Advanced", "Native"];

  languages.forEach((lang, index) => {
    if (!lang.value || !validLanguages.includes(lang.value)) {
      errors.push(`Language ${index + 1}: Please select a valid language`);
    }

    const proficiency = lang.children?.[0]?.value;
    if (!proficiency || !validProficiencies.includes(proficiency)) {
      errors.push(
        `Language ${index + 1}: Please select a valid proficiency level`,
      );
    }
  });

  return errors;
};

export const validateBackgroundSection = (
  backgroundChildren: ValidationItem[],
): string[] => {
  const errors: string[] = [];

  const coreLanguageGroup = backgroundChildren.find(
    (group) => group.value === "core language",
  );
  if (!coreLanguageGroup?.children || coreLanguageGroup.children.length === 0) {
    errors.push("At least one core programming language is required");
  } else {
    coreLanguageGroup.children.forEach(
      (skill: ValidationChild, index: number) => {
        if (!skill.value || skill.value.trim().length === 0) {
          errors.push(
            `Core Language ${index + 1}: Programming language cannot be empty`,
          );
        }
      },
    );
  }

  // Validate other skill groups
  backgroundChildren.forEach((group) => {
    if (
      group.value === "core framework" ||
      group.value === "additional skills"
    ) {
      group.children?.forEach((skill: ValidationChild, index: number) => {
        if (skill.value && skill.value.length > 50) {
          errors.push(
            `${group.value} ${index + 1}: Skill name must be less than 50 characters`,
          );
        }
      });
    }
  });

  return errors;
};

export const validateAvailabilitySection = (
  availability: ValidationItem[],
): string[] => {
  const errors: string[] = [];
  const validOptions = [
    "Available immediately",
    "Available in 2 weeks",
    "Available in 1 month",
    "Not currently available",
  ];

  if (availability.length === 0) {
    errors.push("Availability must be specified");
  } else {
    const selectedAvailability = availability[0]?.value;
    if (!selectedAvailability || !validOptions.includes(selectedAvailability)) {
      errors.push("Please select a valid availability option");
    }
  }

  return errors;
};

// Custom validation for duplicate detection
export const validateNoDuplicates = (values: FormValues): string[] => {
  const errors: string[] = [];

  // Check for duplicate languages
  const languagesSection = values.details.find((d) => d.value === "Languages");
  if (languagesSection?.children) {
    const languages = languagesSection.children.map((l) =>
      l.value.toLowerCase(),
    );
    const duplicateLanguages = languages.filter(
      (lang, index) => languages.indexOf(lang) !== index,
    );
    if (duplicateLanguages.length > 0) {
      errors.push("Duplicate languages are not allowed");
    }
  }

  // Check for duplicate companies in work experience
  const workSection = values.details.find((d) => d.value === "Work Experience");
  if (workSection?.children) {
    const companies = workSection.children.map((w) =>
      w.value.toLowerCase().trim(),
    );
    const duplicateCompanies = companies.filter(
      (company, index) => companies.indexOf(company) !== index,
    );
    if (duplicateCompanies.length > 0) {
      errors.push("Duplicate companies in work experience are not recommended");
    }
  }

  // Check for duplicate institutions in education
  const educationSection = values.details.find((d) => d.value === "Education");
  if (educationSection?.children) {
    const institutions = educationSection.children.map((e) =>
      e.value.toLowerCase().trim(),
    );
    const duplicateInstitutions = institutions.filter(
      (inst, index) => institutions.indexOf(inst) !== index,
    );
    if (duplicateInstitutions.length > 0) {
      errors.push("Duplicate institutions in education are not recommended");
    }
  }

  return errors;
};

// Helper function to validate overlapping work experience dates
export const validateWorkExperienceOverlap = (values: FormValues): string[] => {
  const errors: string[] = [];
  const workSection = values.details.find((d) => d.value === "Work Experience");

  if (!workSection?.children || workSection.children.length < 2) return errors;

  const workPeriods = workSection.children
    .map((work, index) => {
      const durationChild = work.children?.find((c) => c.value === "duration");
      const durationString = durationChild?.children?.[0]?.value || "";

      if (!durationString) return null;

      const parts = durationString.split(" - ");
      if (parts.length !== 2) return null;

      const startDate = new Date(parts[0] + "-01");
      const endDate =
        parts[1] === "Present" ? new Date() : new Date(parts[1] + "-01");

      return { index, startDate, endDate, company: work.value };
    })
    .filter(Boolean);

  // Check for overlapping periods (only warn, don't error)
  for (let i = 0; i < workPeriods.length; i++) {
    for (let j = i + 1; j < workPeriods.length; j++) {
      const period1 = workPeriods[i];
      const period2 = workPeriods[j];

      if (!period1 || !period2) continue;

      const overlap =
        period1.startDate < period2.endDate &&
        period2.startDate < period1.endDate;
      if (overlap) {
        errors.push(
          `Overlapping work periods detected between ${period1.company} and ${period2.company}. Please verify the dates are correct.`,
        );
      }
    }
  }

  return errors;
};

export default employeeProfileValidationSchema;
