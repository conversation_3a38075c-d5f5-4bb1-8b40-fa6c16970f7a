import React from "react";
import { <PERSON>, CircularProgress, <PERSON><PERSON>, Button, Paper, Stack, Typography } from "@mui/material";
import { startOfWeek, endOfWeek, format } from "date-fns";
import { useWeeklyTimelogs, useActiveTimelog } from "@/hooks/tracker";
import WeeklyTimelogCard from "./WeeklyTimelogCard";
import WeekPicker from "@/components/tracker/WeekPicker";
import { WeeklyTimelogListProps } from "@/types/tracker";

const WeeklyTimelogList: React.FC<WeeklyTimelogListProps> = ({
  userId,
  showWeekPicker = true,
  singleWeekView = true,
  initialDate = startOfWeek(new Date(), { weekStartsOn: 1 }),
  onRestartTimer,
}) => {
  const [selectedWeek, setSelectedWeek] = React.useState<Date>(initialDate);

  const { data: activeTimelog } = useActiveTimelog(userId);
  const isTracking = !!activeTimelog;

  const { start: weekStart, end: weekEnd } = React.useMemo(() => {
    const start = format(startOfWeek(selectedWeek, { weekStartsOn: 1 }), "yyyy-MM-dd");
    const end = format(endOfWeek(selectedWeek, { weekStartsOn: 1 }), "yyyy-MM-dd");
    return { start, end };
  }, [selectedWeek]);

  const { data, isLoading, error } = useWeeklyTimelogs({
    userId,
    start_time__gte: singleWeekView ? weekStart : undefined,
    end_time__lte: singleWeekView ? weekEnd : undefined,
    paginate: !singleWeekView,
  });

  const handleWeekChange = (date: Date) => {
    setSelectedWeek(date);
  };

  const sortedResults = React.useMemo(() => {
    if (!data?.results) return [];

    return [...data.results].sort((a, b) => {
      try {
        const dateA = new Date(a.week);
        const dateB = new Date(b.week);

        if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
          return 0;
        }

        return dateB.getTime() - dateA.getTime();
      } catch {
        return 0;
      }
    });
  }, [data?.results]);

  const displayedResults = singleWeekView ? sortedResults : sortedResults.slice(0, 1);

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <CircularProgress size={40} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        sx={{ mx: 2, mt: 2 }}
        action={
          <Button color="inherit" size="small" onClick={() => window.location.reload()}>
            Retry
          </Button>
        }
      >
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Failed to load time logs
          </Typography>
          <Typography variant="body2">
            {error instanceof Error ? error.message : "An unexpected error occurred. Please try again later."}
          </Typography>
        </Box>
      </Alert>
    );
  }

  if (!displayedResults || displayedResults.length === 0) {
    return (
      <>
        {showWeekPicker && (
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">Time Entries</Typography>
            <WeekPicker
              selectedDate={selectedWeek}
              onWeekChange={handleWeekChange}
              weekStartsOn={1}
              showTodayButton={true}
            />
          </Box>
        )}
        <Paper sx={{ p: 3, textAlign: "center" }}>
          <Typography variant="body1" color="textSecondary">
            No time entries found for this period.
          </Typography>
        </Paper>
      </>
    );
  }

  return (
    <Stack spacing={2}>
      {showWeekPicker && (
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">Time Entries</Typography>
          <WeekPicker
            selectedDate={selectedWeek}
            onWeekChange={handleWeekChange}
            weekStartsOn={1}
            showTodayButton={true}
          />
        </Box>
      )}

      {displayedResults.map(weeklyData => (
        <WeeklyTimelogCard
          key={weeklyData.week}
          weeklyData={weeklyData}
          onRestartTimer={onRestartTimer}
          isTracking={isTracking}
        />
      ))}
    </Stack>
  );
};

export default React.memo(WeeklyTimelogList);
