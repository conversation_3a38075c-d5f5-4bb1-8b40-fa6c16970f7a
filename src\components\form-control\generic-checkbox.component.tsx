import React from "react";
import {
  FormControlLabel,
  Checkbox,
  FormControl,
  FormHelperText,
} from "@mui/material";

interface GenericCheckboxProps {
  label: string;
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  color?: "default" | "primary" | "secondary";
}

const GenericCheckbox: React.FC<GenericCheckboxProps> = ({
  label,
  checked,
  onChange,
  name,
  disabled = false,
  error = false,
  helperText,
  color = "primary",
}) => {
  return (
    <FormControl error={error}>
      <FormControlLabel
        control={
          <Checkbox
            checked={checked}
            onChange={onChange}
            name={name}
            disabled={disabled}
            color={color}
          />
        }
        label={label}
      />
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default GenericCheckbox;
