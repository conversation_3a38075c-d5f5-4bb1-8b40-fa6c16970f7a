/**
 * Constants used in Employee Profile forms
 */

export const allSkills = [
  // Programming Languages
  "JavaScript",
  "TypeScript",
  "Python",
  "Java",
  "C#",
  "PHP",
  "Ruby",
  "Go",
  "Rust",
  "Swift",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>ala",
  "C++",
  "C",
  "Dar<PERSON>",
  "R",
  "MATLAB",
  "Perl",
  "Shell/Bash",
  "PowerShell",

  // Frontend Technologies
  "React",
  "Vue.js",
  "Angular",
  "Svelte",
  "HTML5",
  "CSS3",
  "SASS/SCSS",
  "Less",
  "Bootstrap",
  "Tailwind CSS",
  "Material-UI",
  "Ant Design",
  "Chakra UI",
  "Styled Components",

  // Backend Technologies
  "Node.js",
  "Express.js",
  "Django",
  "Flask",
  "Spring Boot",
  "ASP.NET",
  "Laravel",
  "Ruby on Rails",
  "FastAPI",
  "Nest.js",
  "Next.js",
  "Nuxt.js",

  // Databases
  "MongoDB",
  "PostgreSQL",
  "MySQL",
  "SQLite",
  "Redis",
  "Elasticsearch",
  "Firebase",
  "Supabase",

  // Cloud & DevOps
  "AWS",
  "Google Cloud",
  "Azure",
  "Docker",
  "Kubernetes",
  "Jenkins",
  "GitLab CI",
  "GitHub Actions",
  "Terraform",
  "Ansible",
  "Linux",
  "Ubuntu",
  "CentOS",

  // Mobile Development
  "React Native",
  "Flutter",
  "Ionic",
  "Xamarin",
  "Android Development",
  "iOS Development",

  // Other Tools & Technologies
  "Git",
  "GraphQL",
  "REST APIs",
  "Socket.io",
  "WebRTC",
  "Webpack",
  "Vite",
  "Babel",
  "ESLint",
  "Prettier",
];

export const allLanguages = [
  "English",
  "Spanish",
  "French",
  "German",
  "Italian",
  "Portuguese",
  "Dutch",
  "Russian",
  "Chinese (Mandarin)",
  "Chinese (Cantonese)",
  "Japanese",
  "Korean",
  "Arabic",
  "Hindi",
  "Bengali",
  "Punjabi",
  "Tamil",
  "Telugu",
  "Marathi",
  "Gujarati",
  "Urdu",
  "Turkish",
  "Polish",
  "Romanian",
  "Czech",
  "Hungarian",
  "Swedish",
  "Norwegian",
  "Danish",
  "Finnish",
  "Greek",
  "Hebrew",
  "Thai",
  "Vietnamese",
  "Indonesian",
  "Malay",
  "Tagalog",
  "Swahili",
];

export const proficiencyLevels = [
  "Beginner",
  "Intermediate",
  "Advanced",
  "Native",
];

export const availabilityOptions = [
  "Available immediately",
  "Available in 2 weeks",
  "Available in 1 month",
  "Not currently available",
];
