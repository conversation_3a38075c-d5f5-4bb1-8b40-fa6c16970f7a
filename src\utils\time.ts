/**
 * Time-related utility functions
 */

/**
 * Format time in seconds to HH : MM : SS format
 * @param seconds - The number of seconds to format
 * @returns Formatted time string in HH : MM : SS format
 */
export const formatTime = (seconds: number): string => {
  // Ensure we never format negative time
  const safeSeconds = Math.max(0, seconds);
  const hours = Math.floor(safeSeconds / 3600);
  const minutes = Math.floor((safeSeconds % 3600) / 60);
  const secs = safeSeconds % 60;
  return `${hours.toString().padStart(2, "0")} : ${minutes.toString().padStart(2, "0")} : ${secs.toString().padStart(2, "0")}`;
};
