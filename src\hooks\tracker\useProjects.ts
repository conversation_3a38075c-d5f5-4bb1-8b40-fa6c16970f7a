import { useInfiniteQuery, keepPreviousData } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { ProjectOption, ProjectData } from "@/types/tracker";

interface ProjectsResponse {
  results: ProjectData[];
  next: string | null;
  previous: string | null;
  count: number;
}

export const useProjects = (
  searchTerm: string,
  { pageSize = 20, ids = [] }: { pageSize?: number; ids?: string[] } = {}
) => {
  const projectResource = useMemo(() => new Resource("projects"), []);

  const normalizedSearchTerm = searchTerm.trim();
  const idString = ids.join(",");

  const query = useInfiniteQuery({
    queryKey: ["projectListInfinite", normalizedSearchTerm, pageSize, idString],
    queryFn: async ({ pageParam = 1 }) => {
      const params: {
        search: string;
        page?: number;
        page_size?: number;
        id__in?: string;
      } = {
        search: normalizedSearchTerm,
        page: pageParam,
        page_size: pageSize,
      };
      if (ids.length > 0) {
        params.id__in = idString;
        // When fetching by IDs, we don't want pagination
        delete params.page;
        delete params.page_size;
      }
      const response = await projectResource.list(params);
      return response.data;
    },
    enabled: true,
    placeholderData: keepPreviousData,
    retry: 3,
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    getNextPageParam: (lastPage: ProjectsResponse) => {
      if (ids.length > 0 || !lastPage.next) {
        return undefined;
      }
      const url = new URL(lastPage.next);
      return parseInt(url.searchParams.get("page") || "1");
    },
    initialPageParam: 1,
  });

  // Flatten all pages into single array for dropdown
  const projectOptions: ProjectOption[] = useMemo(
    () =>
      query.data?.pages.flatMap(
        page =>
          page.results?.map((project: ProjectData) => ({
            label: project.name || "",
            value: project.id || "",
            space_id: project.clickup_space_id || null,
          })) || []
      ) || [],
    [query.data?.pages]
  );

  return {
    ...query,
    projectOptions,
    hasNextPage: query.hasNextPage,
    fetchNextPage: query.fetchNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    totalCount: query.data?.pages[0]?.count || 0,
    error: query.error,
    isError: query.isError,
    retry: query.refetch,
  };
};
