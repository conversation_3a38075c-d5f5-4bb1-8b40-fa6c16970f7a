function formatErrorObject(error: Record<string, string | string[]>): string {
  const formattedMessages = Object.entries(error)
    .map(([field, messages]) => {
      const label = field === "non_field_errors" ? "Error" : capitalize(field);
      const messageText = Array.isArray(messages)
        ? messages.map(m => String(m).trim()).join("\n")
        : typeof messages === "object"
          ? JSON.stringify(messages)
          : String(messages).trim();

      return `${label}: ${messageText}`;
    })
    .join("\n");

  return formattedMessages;
}

function capitalize(str: string): string {
  return str ? str.charAt(0).toUpperCase() + str.slice(1) : "Unknown";
}

// Until consistent error message is expected from backend, this method ensure proper formating of error message thrown by DRF
export function getFormattedErrorMessage(errorData: unknown): string {
  if (typeof errorData === "string") {
    return errorData;
  }

  if (Array.isArray(errorData)) {
    return errorData.map(String).join(" ");
  }

  if (errorData && typeof errorData === "object") {
    const maybeDetail = (errorData as Record<string, string | string[]>).detail;
    if (typeof maybeDetail === "string") {
      return maybeDetail;
    }

    try {
      return formatErrorObject(errorData as Record<string, string | string[]>);
    } catch {
      return "An unexpected error occurred.";
    }
  }

  return "An unknown error occurred.";
}
