import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { TimelogsResponse, UseTimelogsParams } from "@/types/reports";

const fetchTimelogs = async (
  resource: Resource,
  params: {
    user?: string;
    user__in?: string;
    page?: number;
    page_size?: number;
    start_time__gte?: string;
    end_time__lte?: string;
    paginate?: boolean;
    project?: string;
    project__in?: string;
    is_billable?: boolean;
    is_ot?: boolean;
  }
): Promise<TimelogsResponse> => {
  // Filter out undefined parameters
  const queryParams = Object.fromEntries(Object.entries(params).filter(([, value]) => value !== undefined)) as Record<
    string,
    string | number | boolean
  >;

  const response = await resource.list(queryParams);
  let data = response.data;

  // If the response is an array (when paginate is false), wrap it in an object with a results property
  if (Array.isArray(data)) {
    data = {
      results: data,
      count: data.length,
      next: null,
      previous: null,
    };
  }

  return data;
};

export const useTimelogs = ({
  user__in = undefined,
  page = 1,
  page_size = 100,
  start_time__gte = undefined,
  end_time__lte = undefined,
  paginate = false,
  project__in = undefined,
  is_billable = undefined,
  is_ot = undefined,
  enabled = true,
}: UseTimelogsParams) => {
  const timelogResource = useMemo(() => new Resource("timelogs"), []);

  // Generate query key based on all parameters
  const queryKey = useMemo(
    () =>
      [
        "timelogs",
        user__in,
        paginate ? page : null,
        paginate ? page_size : null,
        start_time__gte,
        end_time__lte,
        paginate,
        project__in,
        is_billable,
        is_ot,
      ].filter(value => {
        // Keep all values except undefined/null, but preserve false for boolean params
        return value !== undefined && value !== null;
      }),
    [user__in, page, page_size, start_time__gte, end_time__lte, paginate, project__in, is_billable, is_ot]
  );

  return useQuery({
    queryKey,
    queryFn: () =>
      fetchTimelogs(timelogResource, {
        ...(user__in && { user__in: user__in.join(",") }),
        ...(paginate && { page, page_size }),
        ...(start_time__gte && { start_time__gte }),
        ...(end_time__lte && { end_time__lte }),
        ...(project__in && { project__in: project__in.join(",") }),
        ...(is_billable !== undefined && { is_billable }),
        ...(is_ot !== undefined && { is_ot }),
        paginate,
      }),
    enabled,
    placeholderData: keepPreviousData,
    staleTime: 0,
    refetchOnWindowFocus: true,
  });
};
