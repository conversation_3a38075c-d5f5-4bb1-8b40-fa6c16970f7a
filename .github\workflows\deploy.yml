name: Remote Build & Deploy

on:
  push:
    branches:
      - development

concurrency:
  group: remote-fe-deploy-queue
  cancel-in-progress: false

jobs:
  deploy:
    name: Remote Build & Deploy
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      - name: Execute remote build via SSH
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -e

            echo "[+] Navigating to project directory..."
            cd "${{ secrets.TARGET_DIR }}"

            echo "[+] Pulling latest changes from development..."
            git reset --hard
            git clean -fd
            git pull origin development

            echo "[+] Installing dependencies..."
            npm install --legacy-peer-deps

            echo "[+] Building the frontend app..."
            npm run build

            echo "[✓] Build completed successfully!"
