import { Box, IconButton, <PERSON>u, MenuItem } from "@mui/material";
import { DataGrid, GridColDef, GridValidRowModel } from "@mui/x-data-grid";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useState } from "react";
import FilterListIcon from "@mui/icons-material/FilterList";
import { TableProps } from "@/interfaces/table.interface";
import { GridFeatureMode } from "@/enums/enum";

const TableComponent: React.FC<TableProps> = ({
  rows,
  columns,
  pageSize,
  loading,
  actions,
  contentAlign,
  hideFooter = true,
  onSortChange,
  onFilterChange,
  onPaginationChange,
  rowCount,
  paginationMode = GridFeatureMode.CLIENT,
  sortingMode = GridFeatureMode.CLIENT,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRow, setSelectedRow] = useState<GridValidRowModel | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>, row: GridValidRowModel) => {
    setAnchorEl(event.currentTarget);
    setSelectedRow(row);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedRow(null);
  };

  const handleAction = (actionHandler: (row: GridValidRowModel) => void) => {
    if (selectedRow && actionHandler) {
      actionHandler(selectedRow);
    }
    handleClose();
  };

  const columnsWithDefaults: GridColDef[] = columns.map(column => ({
    sortable: false,
    filterable: false,
    align: contentAlign || "center",
    headerAlign: contentAlign || "center",
    flex: 1,
    ...column,
  }));

  const columnsWithActions: GridColDef[] = actions?.length
    ? [
        ...columnsWithDefaults,
        {
          field: "actions",
          headerName: "",
          width: 50,
          sortable: false,
          filterable: false,
          disableColumnMenu: true,
          align: contentAlign || "center",
          headerAlign: contentAlign || "center",
          renderCell: params => (
            <IconButton onClick={event => handleClick(event, params.row)} size="small">
              <MoreVertIcon />
            </IconButton>
          ),
        },
      ]
    : columnsWithDefaults;

  return (
    <Box sx={{ width: "100%" }}>
      <DataGrid
        loading={loading}
        rows={rows}
        columns={columnsWithActions}
        getRowHeight={() => "auto"}
        initialState={{
          pagination: {
            rowCount: 0,
            paginationModel: {
              pageSize: pageSize || 10,
            },
          },
        }}
        filterMode="server"
        paginationMode={paginationMode}
        sortingMode={sortingMode}
        pageSizeOptions={[10, 20, 50, 100]}
        onSortModelChange={onSortChange}
        onFilterModelChange={onFilterChange}
        onPaginationModelChange={onPaginationChange}
        disableRowSelectionOnClick
        disableColumnResize
        columnHeaderHeight={35}
        hideFooter={hideFooter}
        rowCount={rowCount || 10}
        slots={{
          columnMenuFilterIcon: () => <FilterListIcon />,
          columnMenuIcon: () => <FilterListIcon />,
        }}
        sx={{
          width: "100%",
          backgroundColor: "transparent",
          "& .MuiDataGrid-cell": {
            backgroundColor: "transparent",
            display: "flex",
            alignItems: "center",
          },
          "& .MuiDataGrid-columnHeaders": {
            backgroundColor: "custom.brand.transparent",
          },
          "& .MuiDataGrid-row": {
            backgroundColor: "transparent",
          },
          "& .MuiDataGrid-columnHeaderTitle": {
            fontWeight: 600,
            display: "flex",
            alignItems: "center",
          },
          "& .MuiDataGrid-row:hover": {
            backgroundColor: "custom.brand.transparent",
          },
          "& .MuiDataGrid-columnHeader": {
            backgroundColor: "transparent",
          },
          "& .MuiDataGrid-container--top [role=row]": {
            backgroundColor: "transparent",
          },
          "& .MuiDataGrid-row.Mui-selected": {
            backgroundColor: "custom.brand.transparent",
          },
          "& .MuiDataGrid-cell:focus-within": {
            outline: "none",
          },
          "& .MuiDataGrid-columnHeaders:focus-within": {
            outline: "none",
          },
          "& .MuiDataGrid-overlay": {
            backgroundColor: "transparent",
          },
          "& .MuiDataGrid-footerContainer": {
            backgroundColor: "transparent",
            height: "35px",
            minHeight: "35px !important",
            "& .MuiTablePagination-root": {
              height: "35px",
            },
            "& .MuiTablePagination-toolbar": {
              minHeight: "35px",
              height: "35px",
              paddingLeft: "5px",
              paddingRight: "5px",
            },
          },
        }}
      />

      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        {actions
          ?.filter(action => !action.shouldShow || (selectedRow && action.shouldShow(selectedRow)))
          .map((action, index) => (
            <MenuItem key={index} onClick={() => handleAction(action.handler)}>
              {action.label}
            </MenuItem>
          ))}
      </Menu>
    </Box>
  );
};

export default TableComponent;
