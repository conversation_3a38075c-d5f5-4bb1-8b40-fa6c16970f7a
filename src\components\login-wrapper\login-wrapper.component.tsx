import { Box, Divider, Typography } from "@mui/material";
import freetimeLogo from "@/assets/freetime.svg";
import outcodeLogo from "@/assets/oc-logo.svg";
import React, { JSX } from "react";

const LoginWrapper: React.FC<{ children: JSX.Element }> = ({ children }) => {
  return (
    <Box
      sx={{
        minHeight: "100vh",
        width: "100vw",
        bgcolor: "#f1f1f2",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        py: 3,
        px: 2,
        gap: 12,
      }}
    >
      <Box>
        <img src={freetimeLogo} alt="Trackify Logo" style={{ height: 80 }} />
      </Box>
      {children}
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Box
          sx={{
            width: "100%",
            mt: 6,
            mb: 2,
            "@media (min-width:600px)": {
              width: "100%",
            },
            "@media (min-width:900px)": {
              width: "50%",
            },
            "@media (min-width:1200px)": {
              width: "40%",
            },
          }}
        >
          <Divider />
        </Box>
        <Box textAlign="center" mb={2} mt={5}>
          <img src={outcodeLogo} alt="Outcode Logo" height="30" />
          <Typography variant="body2" mt={5} fontSize="0.8rem">
            COPYRIGHT © 2020 OUTCODE. ALL RIGHTS RESERVED.
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};
export default LoginWrapper;
