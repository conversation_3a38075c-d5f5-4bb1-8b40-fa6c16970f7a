import { useMemo } from "react";
import {
  Paper,
  Typography,
  Box,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from "recharts";
import { useWeeklySummaryReport } from "@/hooks/tracker/useWeeklySummaryReport";
import { WeeklySummaryProps, User } from "@/types/reports";

export default function WeeklySummary({
  dateRange,
  selectedProjects,
  selectedUsers,
  billableFilter,
  otFilter,
  enabled = true,
}: Omit<
  WeeklySummaryProps,
  "setDateRange" | "handleProjectsChange" | "handleUsersChange" | "setBillableFilter" | "setOtFilter"
>) {
  const { data: reportData = [], isLoading: loading } = useWeeklySummaryReport({
    dateRange,
    selectedProjects,
    selectedUsers,
    billableFilter,
    otFilter,
    enabled,
  });

  const formatDuration = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (days > 0) {
      return `${days} day${days > 1 ? "s" : ""}, ${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    }
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getTotalProjectDuration = (users: User[]) => {
    const totalSeconds = users.reduce((sum, user) => sum + user.total_duration_second, 0);
    return formatDuration(totalSeconds);
  };

  // Prepare chart data
  const chartData = useMemo(() => {
    return reportData.map((project, index) => {
      const totalSeconds = project.users.reduce((sum, user) => sum + user.total_duration_second, 0);
      const totalHours = totalSeconds / 3600;
      const userCount = project.users.length;
      const averageHoursPerUser = userCount > 0 ? totalHours / userCount : 0;

      return {
        name: project.project_name,
        value: Number(totalHours.toFixed(2)),
        color: `hsl(${(index * 137.5) % 360}, 70%, 50%)`,
        totalSeconds,
        formattedDuration: formatDuration(totalSeconds),
        userCount,
        averageHoursPerUser: Number(averageHoursPerUser.toFixed(2)),
      };
    });
  }, [reportData]);

  // Calculate total hours for percentage calculation
  const totalHours = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.value, 0);
  }, [chartData]);

  // Custom tooltip component with detailed information
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload[0]) return null;

    const data = payload[0].payload;
    const percentage = ((data.value / totalHours) * 100).toFixed(1);

    return (
      <Box
        sx={{
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          border: "1px solid #ccc",
          borderRadius: 2,
          padding: 2,
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
          minWidth: 250,
          maxWidth: 350,
        }}
      >
        {/* Project Header */}
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: "bold",
            color: data.color,
            mb: 1,
            borderBottom: `2px solid ${data.color}`,
            pb: 0.5,
          }}
        >
          {data.name}
        </Typography>

        {/* Main Statistics */}
        <Box sx={{ mb: 1.5 }}>
          <Typography variant="body2" sx={{ mb: 0.5, display: "flex", alignItems: "center" }}>
            <Box component="span" sx={{ mr: 0.5 }}>
              ⏱️
            </Box>
            <strong>Total Time:</strong>
            <Box component="span" sx={{ ml: 0.5, color: data.color, fontWeight: "bold" }}>
              {data.formattedDuration} ({data.value} hours)
            </Box>
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5, display: "flex", alignItems: "center" }}>
            <Box component="span" sx={{ mr: 0.5 }}>
              📊
            </Box>
            <strong>Percentage:</strong>
            <Box component="span" sx={{ ml: 0.5, color: data.color, fontWeight: "bold" }}>
              {percentage}% of total time
            </Box>
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5, display: "flex", alignItems: "center" }}>
            <Box component="span" sx={{ mr: 0.5 }}>
              👥
            </Box>
            <strong>Contributors:</strong>
            <Box component="span" sx={{ ml: 0.5 }}>
              {data.userCount} user{data.userCount !== 1 ? "s" : ""}
            </Box>
          </Typography>
          {data.userCount > 1 && (
            <Typography variant="body2" sx={{ mb: 0.5, display: "flex", alignItems: "center" }}>
              <Box component="span" sx={{ mr: 0.5 }}>
                📈
              </Box>
              <strong>Average per user:</strong>
              <Box component="span" sx={{ ml: 0.5 }}>
                {data.averageHoursPerUser} hours
              </Box>
            </Typography>
          )}
        </Box>
      </Box>
    );
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderCustomLabel = (props: any) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, value } = props;

    if (!cx || !cy || midAngle === undefined || !innerRadius || !outerRadius || !value) return null;

    const percentage = (value / totalHours) * 100;

    // Only show label if slice is >= 5% of total to avoid overlap
    if (percentage < 5) return null;

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${value}h`}
      </text>
    );
  };

  // Custom legend formatter
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderLegend = (props: any) => {
    const { payload } = props;
    if (!payload) return null;

    return (
      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          justifyContent: "center",
          gap: { xs: 1, sm: 2 },
          mt: 2,
          maxWidth: "100%",
          px: { xs: 1, sm: 2 },
        }}
      >
        {[...payload]
          .sort((a, b) => (b.payload?.value || b.value) - (a.payload?.value || a.value))
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .map((entry: any, index: number) => (
            <Box
              key={`legend-${index}`}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 0.5,
                minWidth: { xs: "100px", sm: "120px" },
                maxWidth: { xs: "140px", sm: "200px" },
                flexShrink: 0,
                fontSize: { xs: "0.7rem", sm: "0.75rem" },
              }}
            >
              <Box
                sx={{
                  width: { xs: 10, sm: 12 },
                  height: { xs: 10, sm: 12 },
                  backgroundColor: entry.color,
                  borderRadius: "2px",
                  flexShrink: 0,
                }}
              />
              <Typography
                variant="caption"
                sx={{
                  fontSize: { xs: "0.65rem", sm: "0.75rem" },
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                }}
                title={`${entry.value}: ${entry.payload?.value || entry.value}h`}
              >
                {entry.value}: {entry.payload?.value || entry.value}h
              </Typography>
            </Box>
          ))}
      </Box>
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Weekly Time Summary
      </Typography>

      {reportData.length === 0 ? (
        <Typography variant="body1" color="text.secondary">
          No data available for the selected filters.
        </Typography>
      ) : (
        <Box>
          {/* Chart Section */}
          <Box sx={{ mb: 4 }}>
            <Box
              sx={{
                width: "100%",
                height: { xs: 400, sm: 450, md: 500 },
                position: "relative",
              }}
            >
              <ResponsiveContainer>
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="45%"
                    labelLine={false}
                    label={renderCustomLabel}
                    outerRadius="60%"
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={CustomTooltip} />
                  <Legend
                    content={renderLegend}
                    wrapperStyle={{
                      paddingTop: "20px",
                      fontSize: "12px",
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Box>

          {/* Detailed Data Section */}
          <Typography variant="h6" gutterBottom>
            Detailed Breakdown
          </Typography>
          {reportData.map(project => (
            <Accordion key={project.project_id} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: "flex", justifyContent: "space-between", width: "100%", pr: 2 }}>
                  <Typography variant="h6">{project.project_name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total: {getTotalProjectDuration(project.users)} ({project.users.length} user
                    {project.users.length !== 1 ? "s" : ""})
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>User</TableCell>
                        <TableCell align="right">Duration</TableCell>
                        <TableCell align="right">Total Seconds</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {project.users.map(user => (
                        <TableRow key={user.user_id}>
                          <TableCell>{user.full_name}</TableCell>
                          <TableCell align="right">{user.total_duration}</TableCell>
                          <TableCell align="right">{user.total_duration_second.toLocaleString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}
    </Box>
  );
}
