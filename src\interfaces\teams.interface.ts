export interface Group {
  id: string | number;
  name: string;
  name_display: string;
}

export interface IUserForm {
  id?: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  email: string;
  is_active?: boolean;
  groups: Group[];
  phone?: string;
  country?: string;
  timezone?: string;
}

export interface IUserDetail {
  phone?: string;
  country_display?: string;
  country?: string;
  image?: string;
  timezone?: string;
}

export interface IUserModalBox {
  handleClose?: (value: boolean) => void;
}

export interface ITeam {
  id: string;
  name?: string;
  name_display?: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  email: string;
  is_active: boolean;
  phone?: string;
  country?: string;
  is_invited: boolean;
  profile_image?: string;
  groups: Group[];
  detail: IUserDetail;
  timezone?: string;
}

export interface IGroup {
  id: string;
  name: string;
  name_display: string;
}

export interface IGroupFrom {
  id?: string;
  name: string;
  name_display: string;
}

export interface IUserInvitePayload {
  first_name: string;
  last_name: string;
  email: string;
  groups: number[];
  middle_name?: string;
  phone?: string;
  country?: string;
  timezone?: string;
}
export interface IEditInvitePayload {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  groups: number[];
}

export interface IEditUserPayload {
  id: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  email: string;
  group_ids: number[];
  phone?: string;
  country?: string;
  timezone?: string;
  is_active?: boolean;
}

export interface Identifiable {
  id: number | string;
}

export interface ManagePermissionsProps {
  group: Group;
}
export interface Permission {
  id: number;
  name: string;
}
