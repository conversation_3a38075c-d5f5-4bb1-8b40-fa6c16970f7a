import { FormValues } from "./employee-profile-form-utils";
import {
  validateWorkExperienceSection,
  validateEducationSection,
  validateLanguagesSection,
  validateBackgroundSection,
  validateAvailabilitySection,
} from "./employee-profile-validation";

/**
 * Get field-specific validation errors for a form section
 */
export const getFieldErrors = (
  values: FormValues,
  sectionType: string,
  sectionIndex: number,
) => {
  const section = values.details[sectionIndex];
  if (!section || section.value !== sectionType) return {};

  const errors: Record<string, string[]> = {};

  switch (sectionType) {
    case "Work Experience":
      if (section.children) {
        const workErrors = validateWorkExperienceSection(section.children);
        section.children.forEach((_, workIndex) => {
          const workSpecificErrors = workErrors.filter((error) =>
            error.includes(`Work Experience ${workIndex + 1}`),
          );
          if (workSpecificErrors.length > 0) {
            errors[`work_${workIndex}`] = workSpecificErrors;
          }
        });
      }
      break;

    case "Education":
      if (section.children) {
        const eduErrors = validateEducationSection(section.children);
        section.children.forEach((_, eduIndex) => {
          const eduSpecificErrors = eduErrors.filter((error) =>
            error.includes(`Education ${eduIndex + 1}`),
          );
          if (eduSpecificErrors.length > 0) {
            errors[`education_${eduIndex}`] = eduSpecificErrors;
          }
        });
      }
      break;

    case "Languages":
      if (section.children) {
        const langErrors = validateLanguagesSection(section.children);
        section.children.forEach((_, langIndex) => {
          const langSpecificErrors = langErrors.filter((error) =>
            error.includes(`Language ${langIndex + 1}`),
          );
          if (langSpecificErrors.length > 0) {
            errors[`language_${langIndex}`] = langSpecificErrors;
          }
        });
      }
      break;

    case "Background":
      if (section.children) {
        const bgErrors = validateBackgroundSection(section.children);
        if (bgErrors.length > 0) {
          errors.background = bgErrors;
        }
      }
      break;

    case "Availability":
      if (section.children) {
        const availErrors = validateAvailabilitySection(section.children);
        if (availErrors.length > 0) {
          errors.availability = availErrors;
        }
      }
      break;
  }

  return errors;
};

/**
 * Check if a specific field has validation errors
 */
export const hasFieldError = (
  values: FormValues,
  sectionType: string,
  sectionIndex: number,
  fieldKey: string,
): boolean => {
  const errors = getFieldErrors(values, sectionType, sectionIndex);
  return !!(errors[fieldKey] && errors[fieldKey].length > 0);
};

/**
 * Get error message for a specific field
 */
export const getFieldErrorMessage = (
  values: FormValues,
  sectionType: string,
  sectionIndex: number,
  fieldKey: string,
): string => {
  const errors = getFieldErrors(values, sectionType, sectionIndex);
  if (errors[fieldKey] && errors[fieldKey].length > 0) {
    return errors[fieldKey][0] || ""; // Return first error message or empty string
  }
  return "";
};

/**
 * Validate a single field and return immediate feedback
 */
export const validateSingleField = (
  fieldType: string,
  value: string,
): string | null => {
  switch (fieldType) {
    case "company":
      if (!value || value.trim().length < 2) {
        return "Company name is required and must be at least 2 characters";
      }
      if (value.length > 100) {
        return "Company name must be less than 100 characters";
      }
      if (!/^[a-zA-Z0-9\s.,&'-]+$/.test(value)) {
        return "Company name contains invalid characters";
      }
      break;

    case "role":
      if (!value || value.trim().length < 2) {
        return "Role is required and must be at least 2 characters";
      }
      if (value.length > 100) {
        return "Role must be less than 100 characters";
      }
      break;

    case "description":
      if (!value || value.trim().length < 10) {
        return "Description is required and must be at least 10 characters";
      }
      if (value.length > 2000) {
        return "Description must be less than 2000 characters";
      }
      break;

    case "institution":
      if (!value || value.trim().length < 2) {
        return "Institution name is required and must be at least 2 characters";
      }
      if (value.length > 100) {
        return "Institution name must be less than 100 characters";
      }
      break;

    case "degree":
      if (!value || value.trim().length < 2) {
        return "Degree is required and must be at least 2 characters";
      }
      if (value.length > 100) {
        return "Degree must be less than 100 characters";
      }
      break;

    case "graduationYear":
      if (value) {
        const year = parseInt(value);
        const currentYear = new Date().getFullYear();
        if (isNaN(year) || year < 1950 || year > currentYear + 6) {
          return `Graduation year must be between 1950 and ${currentYear + 6}`;
        }
      }
      break;
    case "duration": {
      if (!value) {
        return "Duration is required";
      }
      const parts = value.split(" - ");
      if (parts.length !== 2) {
        return "Invalid duration format";
      }
      const startDate = parts[0];
      const endDate = parts[1];

      if (!startDate || !endDate) {
        return "Invalid duration format";
      }

      const isCurrent = endDate === "Present";

      // Validate formatted date strings like "Jun 2025", "Dec 2023"
      const dateFormatRegex = /^[A-Za-z]{3}\s\d{4}$/;
      if (!dateFormatRegex.test(startDate)) {
        return "Invalid start date format";
      }

      if (!isCurrent && !dateFormatRegex.test(endDate)) {
        return "Invalid end date format";
      }

      // Parse and validate actual dates
      try {
        const startDateObj = new Date(startDate + " 01");
        if (isNaN(startDateObj.getTime())) {
          return "Invalid start date";
        }

        if (startDateObj > new Date()) {
          return "Start date cannot be in the future";
        }

        if (!isCurrent) {
          const endDateObj = new Date(endDate + " 01");
          if (isNaN(endDateObj.getTime())) {
            return "Invalid end date";
          }

          if (endDateObj > new Date()) {
            return "End date cannot be in the future";
          }

          if (endDateObj <= startDateObj) {
            return "End date must be after start date";
          }
        }
      } catch {
        return "Invalid date format";
      }
      break;
    }
  }

  return null;
};
