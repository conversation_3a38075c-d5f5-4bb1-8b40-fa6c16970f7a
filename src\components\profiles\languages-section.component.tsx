import React from "react";
import {
  Button,
  Grid,
  IconButton,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import { DetailNode } from "@/utils/employee-profile-form-utils";
import { allLanguages, proficiencyLevels } from "./constants";

interface LanguagesSectionProps {
  sectionIndex: number;
  section: DetailNode;
  setFieldValue: (field: string, value: unknown) => void;
}

const LanguagesSection: React.FC<LanguagesSectionProps> = ({ sectionIndex, section, setFieldValue }) => {
  const addLanguage = () => {
    const currentLanguages = section.children || [];
    const newLanguages = [...currentLanguages, { value: "", children: [{ value: "" }] }];
    setFieldValue(`details[${sectionIndex}].children`, newLanguages);
  };

  const removeLanguage = (index: number) => {
    const currentLanguages = section.children || [];
    const newLanguages = currentLanguages.filter((_, i) => i !== index);
    setFieldValue(`details[${sectionIndex}].children`, newLanguages);
  };

  const updateLanguage = (index: number, language: string, proficiency: string) => {
    const currentLanguages = section.children || [];
    const newLanguages = [...currentLanguages];
    newLanguages[index] = {
      value: language,
      children: [{ value: proficiency }],
    };
    setFieldValue(`details[${sectionIndex}].children`, newLanguages);
  };

  return (
    <Box>
      {section.children?.map((language, index) => (
        <Grid container spacing={2} key={index} sx={{ mb: 2, alignItems: "center" }}>
          <Grid size={5}>
            <FormControl fullWidth error={!language.value}>
              <InputLabel>Language</InputLabel>
              <Select
                value={language.value || ""}
                onChange={e => updateLanguage(index, e.target.value as string, language.children?.[0]?.value || "")}
                label="Language"
              >
                {allLanguages.map(lang => (
                  <MenuItem key={lang} value={lang}>
                    {lang}
                  </MenuItem>
                ))}
              </Select>
              {!language.value && <FormHelperText>Language is required</FormHelperText>}
            </FormControl>
          </Grid>
          <Grid size={5}>
            <FormControl fullWidth error={!language.children?.[0]?.value}>
              <InputLabel>Proficiency</InputLabel>
              <Select
                value={language.children?.[0]?.value || ""}
                onChange={e => updateLanguage(index, language.value, e.target.value as string)}
                label="Proficiency"
              >
                {proficiencyLevels.map(level => (
                  <MenuItem key={level} value={level}>
                    {level}
                  </MenuItem>
                ))}
              </Select>
              {!language.children?.[0]?.value && <FormHelperText>Proficiency level is required</FormHelperText>}
            </FormControl>
          </Grid>
          <Grid size={2}>
            <IconButton onClick={() => removeLanguage(index)} color="error">
              <RemoveCircleOutlineIcon />
            </IconButton>
          </Grid>
        </Grid>
      ))}
      <Button startIcon={<AddCircleOutlineIcon />} onClick={addLanguage} variant="outlined" size="small">
        Add Language
      </Button>
    </Box>
  );
};

export default LanguagesSection;
