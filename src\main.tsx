import { CircularProgress, CssBaseline, ThemeProvider } from "@mui/material";
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import App from "@/App";
import ErrorBoundary from "@/screens/errors/ErrorBoundary";
import { persistor, store } from "@/store/store";
import theme from "@/theme/theme";
import init from "@/init";
import { ToastContainer } from "react-toastify";

// Initialize React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 3,
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnReconnect: true,
      retryDelay: 1000,
    },
    mutations: {
      retry: false,
    },
  },
});
init();

ReactDOM.createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <CssBaseline />
    <ThemeProvider theme={theme}>
      <ErrorBoundary>
        <Provider store={store}>
          <PersistGate loading={<CircularProgress />} persistor={persistor}>
            <QueryClientProvider client={queryClient}>
              <App />
              <ToastContainer position="top-right" autoClose={2000} />
            </QueryClientProvider>
          </PersistGate>
        </Provider>
      </ErrorBoundary>
    </ThemeProvider>
  </StrictMode>
);
