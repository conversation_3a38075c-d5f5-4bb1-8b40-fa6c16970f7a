export interface IProfileShare {
  id: string;
  profileId: string;
  sharedWithEmail?: string;

  accessLevel: "view";
  expiresAt?: Date | string;
  createdAt: Date | string;
  updatedAt: Date | string;
  isActive: boolean;
  shareLink: string;
  notes?: string;
}

export interface IProfileShareInput
  extends Omit<IProfileShare, "id" | "createdAt" | "updatedAt" | "shareLink"> {
  accessLevel: "view";
}
