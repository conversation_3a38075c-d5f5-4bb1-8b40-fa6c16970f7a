import { useEffect, useState } from "react";
import { Box, Typography, Button, Select, MenuItem, Paper, IconButton, InputBase } from "@mui/material";
import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb";
import { IActiveBreadcrumbProps } from "@/interfaces/activeBreadcrumbProps.interface";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import Resource from "@/core/api/resource";
import { Client } from "@/interfaces/client.interface";
import { ClientFormData, clientStatusLabels, UpdateClientFormData } from "@/interfaces/client.interface";
import { useMutation, useQuery, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import TableComponent from "@/components/table/table.component";
import { GridColDef, GridFilterModel, GridPaginationModel, GridSortModel } from "@mui/x-data-grid";
import { GridFeatureMode } from "@/enums/enum";
import ToastMessage from "@/components/toast/toast.component";

import SearchIcon from "@mui/icons-material/Search";
import theme from "@/theme/theme";
import ClientForm from "./client-form.screen";

const ClientsListScreen = () => {
  const queryClient = useQueryClient();
  const clientResource = new Resource("clients");

  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [clientQueryParams, setClientQueryParams] = useState({
    page: 1,
    pageSize: 10,
    ordering: "updated_at",
    search: "",
    status: "",
  });
  const [searchTerm, setSearchTerm] = useState(clientQueryParams.search);
  const [isCreateUpdateModelOpen, setIsCreateUpdateModelOpen] = useState(false);
  const [isDeleteModelOpen, setIsDeleteModelOpen] = useState(false);

  const query = {
    page: clientQueryParams.page,
    "page-size": clientQueryParams.pageSize,
    ordering: clientQueryParams.ordering,
    search: clientQueryParams.search,
    status: clientQueryParams.status,
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      setClientQueryParams(prev => ({
        ...prev,
        search: searchTerm,
      }));
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  const {
    //allClients
    data: allClients,
    isLoading,
  } = useQuery({
    queryKey: ["allClients", JSON.stringify(query)],
    queryFn: async () => {
      const response = await clientResource.list(query);
      return {
        rows: response.data.results,
        count: response.data.count,
      };
    },
    placeholderData: keepPreviousData,
  });

  const trackerPageBreadCrumbs: IActiveBreadcrumbProps = {
    color: theme.palette.primary.main,
    links: [
      {
        text: "Clients",
        link: "/clients",
        customColor: theme.palette.text.primary,
      },
    ],
  };
  const openClientCreateModel = () => {
    setIsCreateUpdateModelOpen(true);
  };
  const handleCloseModel = () => {
    setIsCreateUpdateModelOpen(false);
    setSelectedClient(null);
  };

  const rows = allClients?.rows;
  const columns: GridColDef<(typeof rows)[number]>[] = [
    {
      field: "name",
      headerName: "Client",
      sortable: true,
      filterable: true,
    },
    {
      field: "email",
      headerName: "Email",
      sortable: true,
      filterable: true,
    },
    {
      field: "address",
      headerName: "Address",
      sortable: true,
      filterable: true,
    },
    {
      field: "country",
      headerName: "Country",
      sortable: true,
      filterable: true,
    },
    {
      field: "phone",
      headerName: "Phone",
      sortable: true,
      filterable: true,
    },
    {
      field: "status",
      headerName: "Status",
      sortable: true,
      filterable: true,
      valueFormatter: status => {
        return clientStatusLabels[status as keyof typeof clientStatusLabels] || "Unknown";
      },
    },
  ];

  // Edit Handler
  const handleRowEdit = (client: Client) => {
    setSelectedClient(client);
    setIsCreateUpdateModelOpen(true);
  };
  // Edit Handler

  //Client Delete
  const { mutate: deleteClientMutation, isPending: deleteInProgress } = useMutation({
    mutationKey: ["deleteClient"],
    mutationFn: async (id: string) => {
      return await clientResource.destroy(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["allClients"] });
      ToastMessage.success("Client deleted successfully.");
      handleClose();
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
      handleClose();
    },
  });

  const handleRowDelete = (client: Client) => {
    setSelectedClient(client);
    setIsDeleteModelOpen(true);
  };
  const handleClose = () => {
    setSelectedClient(null);
  };
  //Client Delete

  // Client Create Mutation
  const { mutate: createClientMutation, isPending: createClientLoading } = useMutation({
    mutationKey: ["createClient"],
    mutationFn: async (data: ClientFormData | UpdateClientFormData) => {
      return await clientResource.store({
        ...data,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["allClients"] });
      ToastMessage.success("Client created successfully.");
      setIsCreateUpdateModelOpen(false);
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  // Client Update Mutation
  const { mutate: updateClientMutation, isPending: updateClientLoading } = useMutation({
    mutationKey: ["updateClientMutation"],
    mutationFn: async (data: UpdateClientFormData & { id: string }) => {
      const { id, ...rest } = data;
      return await clientResource.patch(id, rest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["allClients"] });
      ToastMessage.success("Client updated successfully.");
      setIsCreateUpdateModelOpen(false);
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  //Client Create/Update

  //Client Sort Handler
  const handleSortTable = (model: GridSortModel) => {
    const sortField = model[0]?.field || "";
    const sortDirection = model[0]?.sort || "";

    if (sortField && sortDirection) {
      setClientQueryParams(prev => ({
        ...prev,
        ordering: `${sortDirection === "desc" ? "-" : ""}${sortField}`,
      }));
    }
    return model;
  };

  //Client Filter Handler
  const handleFilterTable = (model: GridFilterModel) => {
    return model;
  };

  //Project Pagination
  const handlePaginationChange = (model: GridPaginationModel) => {
    setClientQueryParams(prev => ({
      ...prev,
      page: model.page + 1,
      pageSize: model.pageSize,
    }));
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 3,
        p: 3,
      }}
    >
      <Box>
        <ActiveBreadcrumb {...trackerPageBreadCrumbs}></ActiveBreadcrumb>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h1">Clients</Typography>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Button variant="contained" onClick={() => openClientCreateModel()} startIcon={<AddOutlinedIcon />}>
              Create Client
            </Button>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          p: 0,
          mb: 0,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
          flexWrap: "nowrap",
        }}
      >
        <Paper
          component="div"
          sx={{
            p: 0,
            display: "flex",
            alignItems: "center",
            minWidth: 150,
            flexShrink: 0,
            height: 40, // set height explicitly or use same as search input
          }}
        >
          <Select
            value={clientQueryParams.status}
            onChange={e =>
              setClientQueryParams(prev => ({
                ...prev,
                status: e.target.value,
              }))
            }
            displayEmpty
            size="small"
            fullWidth
            sx={{
              height: "100%",
              "& .MuiSelect-select": {
                display: "flex",
                alignItems: "center",
                height: "100%",
                boxSizing: "border-box",
              },
            }}
          >
            <MenuItem value="">
              <em>All Status</em>
            </MenuItem>
            {Object.entries(clientStatusLabels).map(([value, label]) => (
              <MenuItem key={value} value={value}>
                {label}
              </MenuItem>
            ))}
          </Select>
        </Paper>

        <Paper
          component="form"
          sx={{
            p: "2px 4px",
            display: "flex",
            alignItems: "center",
            flex: 1,
            height: 40,
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search..."
            inputProps={{ "aria-label": "search" }}
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
          <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
            <SearchIcon />
          </IconButton>
        </Paper>
      </Box>

      <Box>
        <TableComponent
          contentAlign="left"
          columns={columns}
          rows={rows}
          rowCount={allClients?.count}
          loading={isLoading}
          hideFooter={false}
          paginationMode={GridFeatureMode.SERVER}
          sortingMode={GridFeatureMode.SERVER}
          actions={[
            {
              label: "Edit",
              handler: client => {
                handleRowEdit(client as Client);
              },
            },
            {
              label: "Delete",
              handler: client => {
                handleRowDelete(client as Client);
              },
            },
          ]}
          onSortChange={model => {
            handleSortTable(model);
          }}
          onFilterChange={model => {
            handleFilterTable(model);
          }}
          onPaginationChange={model => {
            handlePaginationChange(model);
          }}
        />
      </Box>
      <Dialog // Delete
        open={Boolean(isDeleteModelOpen && selectedClient?.id)}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Confirm Client Deletion"}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description" sx={{ color: "grey.900" }}>
            Are you sure you want to delete the client <strong>{selectedClient?.name}</strong>? This action is
            irreversible and will permanently remove all associated data.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handleClose()}>Cancel</Button>
          <Button
            onClick={() => deleteClientMutation(selectedClient?.id ? selectedClient.id : "")}
            disabled={!selectedClient?.id || deleteInProgress}
            loading={deleteInProgress}
            loadingPosition="end"
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={isCreateUpdateModelOpen} onClose={handleCloseModel}>
        <DialogTitle>{selectedClient ? "Edit Client" : "Create Client"}</DialogTitle>
        <DialogContent>
          <Box sx={{ maxWidth: "100vw", margin: "left auto", marginTop: "7px" }}>
            <ClientForm
              selectedClient={selectedClient}
              onClose={handleCloseModel}
              onSubmit={data => {
                if ("id" in data && data.id) {
                  updateClientMutation(data as UpdateClientFormData & { id: string });
                } else {
                  createClientMutation(data as ClientFormData);
                }
              }}
              isSubmitting={createClientLoading || updateClientLoading}
            />
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ClientsListScreen;
