export interface SelectorTypes {
  user: {
    user: User | null;
  };
}

export interface User {
  id: string;
  created_at: string;
  updated_at: string;
  email: string;
  first_name: string;
  last_name: string;
  middle_name?: string | null;
  date_of_birth?: string;
  zipcode?: string;
  linkedin_url?: string;
  engagement_score?: string;
  classification?: string;
  phone?: string;
  image_url?: string;
  image_url_full?: string;
  currently_employeed_here?: boolean;
}

export interface IAuth {
  email: string;
  passowrd: string;
  confirmPassword?: string;
}

export interface AuthResponse {
  user: User | null;
  groups: string[];
  tenant: ITanent | null;
  isAuthenticated: boolean;
  stay_logged_in?: boolean;
}

export interface AuthState extends AuthResponse {
  isAuthenticated: boolean;
  stay_logged_in: boolean;
}

export interface ITanent {
  id: string;
  company_name: string;
  is_active: boolean;
}

export interface NavigateOptions {
  replace?: boolean;
  state?: unknown;
}

// interface for group autocomplete member
export interface IGroupUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

export interface UseGroupUsersData {
  results: IGroupUser[];
}

export interface UseGroupUsersResult {
  data?: UseGroupUsersData;
  isLoading: boolean;
}

export interface UseGroupUsersHook {
  (groupId: string): UseGroupUsersResult;
}

export interface MembersList {
  data?: {
    results: IGroupUser[];
  };
}

export interface GroupUserAutocompleteProps {
  groupId: string;
  useGroupUsers: UseGroupUsersHook;
  membersList?: MembersList;
  refetchMemberList?: () => void;
  refetchGroupUsers?: () => void;
}

export interface IPermission {
  id: number;
  codename: string;
  name: string;
  content_type: number;
}

export interface IPermissionProps {
  permissions: IPermission[];
}
