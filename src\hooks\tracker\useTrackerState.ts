import { useState, useRef, useCallback, useEffect } from "react";
import { ProjectOption, FormValues } from "@/types/tracker";
import {
  useProjects,
  useActiveTimelog,
  useClickupTasks,
  useClickupTaskById,
  useElapsedTimer,
  useTimerMutations,
  useDebounce,
  useUserProfile,
} from "@/hooks/tracker";
import ToastMessage from "@/components/toast/toast.component";
import { UseTrackerStateProps } from "@/interfaces/tracker.interface";

export const useTrackerState = ({ userId, onTrackerStateChange, setFieldValueRef }: UseTrackerStateProps) => {
  const projectDebounce = useDebounce(300);
  const taskDebounce = useDebounce(300);

  const [selectedProject, setSelectedProject] = useState<ProjectOption | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [currentTimelogId, setCurrentTimelogId] = useState<string | null>(null);

  const previousDescription = useRef<string>("");
  const previousProjectId = useRef<string | number | null>(null);
  const hasInitializedProject = useRef(false);

  const { projectOptions, isLoading: projectsLoading } = useProjects(projectDebounce.debouncedValue);
  const { data: activeTimelog, isLoading: activeTimelogLoading } = useActiveTimelog(userId);
  const { data: userProfile } = useUserProfile();

  const isClickUpConnected =
    userProfile?.connected_service?.some(service => service.type.name === "clickup" && service.type.is_enable) ?? false;

  const {
    taskOptions,
    isLoading: tasksLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    isError: tasksError,
    error: taskErrorDetails,
    retry: retryTasks,
  } = useClickupTasks(selectedProject?.space_id, taskDebounce.debouncedValue);

  const { task: specificTask, isLoading: specificTaskLoading } = useClickupTaskById(
    selectedProject?.space_id,
    activeTimelog?.clickup_task_id
  );

  const { formattedTime, setElapsedTime } = useElapsedTimer(isTracking, activeTimelog?.start_time);

  const { startTimerMutation, stopTimerMutation, updateTimelogMutation } = useTimerMutations(
    userId,
    data => {
      setCurrentTimelogId(data.id);
      setIsTracking(true);
      setElapsedTime(0);
      onTrackerStateChange?.(true);
    },
    resetForm => {
      setIsTracking(false);
      setCurrentTimelogId(null);
      setSelectedProject(null);
      projectDebounce.clearDebouncedValue();
      taskDebounce.clearDebouncedValue();
      if (resetForm) {
        resetForm();
      }
      onTrackerStateChange?.(false);
    },
    () => {
      ToastMessage.success("Tracker updated successfully");
    },
    () => {
      ToastMessage.error("Failed to update tracker. Please try again.");
    }
  );

  const handleFieldUpdate = useCallback(
    (field: string, value: string | boolean) => {
      if (!isTracking || !currentTimelogId) return;

      // Client-side validation before sending to backend
      if (field === "project" && (!value || (typeof value === "string" && value.trim() === ""))) {
        ToastMessage.error("Project is required");
        return;
      }

      if (field === "description" && typeof value === "string") {
        const trimmedValue = value.trim();
        if (trimmedValue === "") {
          ToastMessage.error("Description is required");
          return;
        }
        if (trimmedValue.length > 500) {
          ToastMessage.error("Description must be less than 500 characters");
          return;
        }
      }

      if (field === "clickupTaskName" && !selectedProject) {
        ToastMessage.error("Please select a project first");
        return;
      }

      const updateData: {
        project?: string;
        clickup_task_id?: string;
        description?: string;
        is_billable?: boolean;
        is_ot?: boolean;
      } = {};

      switch (field) {
        case "project":
          updateData.project = value as string;
          break;
        case "clickupTaskName":
          updateData.clickup_task_id = value as string;
          break;
        case "description":
          // For description, we use the trimmed value since we validated it above
          updateData.description = (value as string).trim();
          break;
        case "isBillable":
          updateData.is_billable = value as boolean;
          break;
        case "isOT":
          updateData.is_ot = value as boolean;
          break;
      }

      updateTimelogMutation.mutate({
        timelogId: currentTimelogId,
        updateData,
      });
    },
    [isTracking, currentTimelogId, selectedProject, updateTimelogMutation]
  );

  const handleStartTimer = useCallback(
    async (values: FormValues) => {
      if (currentTimelogId) {
        ToastMessage.error("You already have an active timer running. Please stop it before starting a new one.");
        return;
      }
      startTimerMutation.mutate(values);
    },
    [currentTimelogId, startTimerMutation]
  );

  const handleStopTimer = useCallback(
    (resetForm?: () => void) => {
      if (currentTimelogId && activeTimelog) {
        stopTimerMutation.mutate({
          timelogId: currentTimelogId,
          timelogData: activeTimelog,
          resetForm,
        });
      }
    },
    [currentTimelogId, activeTimelog, stopTimerMutation]
  );

  const handleProjectSelect = useCallback(
    (project: ProjectOption | null) => {
      const currentProjectId = project?.value || null;
      if (previousProjectId.current !== currentProjectId && previousProjectId.current !== null) {
        if (isTracking) {
          handleFieldUpdate("clickupTaskName", "");
        }
        if (setFieldValueRef?.current) {
          setFieldValueRef.current("clickupTaskName", "");
        }
        taskDebounce.clearDebouncedValue();
      }
      previousProjectId.current = currentProjectId;
      setSelectedProject(project);
    },
    [isTracking, handleFieldUpdate, taskDebounce, setFieldValueRef]
  );

  useEffect(() => {
    if (activeTimelog && !hasInitializedProject.current) {
      setCurrentTimelogId(activeTimelog.id);
      setIsTracking(true);
      hasInitializedProject.current = true;
      onTrackerStateChange?.(true);
    } else if (!activeTimelog) {
      setCurrentTimelogId(null);
      setIsTracking(false);
      hasInitializedProject.current = false;
      onTrackerStateChange?.(false);
    }
  }, [activeTimelog, onTrackerStateChange]);

  useEffect(() => {
    if (activeTimelog && activeTimelog.project && projectOptions.length > 0 && !selectedProject) {
      const matchingProject = projectOptions.find(
        (option: { value: string }) => option.value === activeTimelog.project
      );
      if (matchingProject) {
        setSelectedProject(matchingProject);
      }
    }
  }, [activeTimelog, projectOptions, selectedProject]);

  useEffect(() => {
    if (
      activeTimelog &&
      activeTimelog.clickup_task_id &&
      (selectedProject?.space_id || (activeTimelog.project && isClickUpConnected)) &&
      setFieldValueRef?.current
    ) {
      const taskValue = specificTask ? specificTask.task_id : activeTimelog.clickup_task_id;
      setFieldValueRef.current("clickupTaskName", taskValue);

      taskDebounce.clearDebouncedValue();
    }
  }, [activeTimelog, selectedProject, specificTask, taskDebounce, setFieldValueRef, isClickUpConnected]);

  useEffect(() => {
    if (!isClickUpConnected && setFieldValueRef?.current) {
      setFieldValueRef.current("clickupTaskName", "");
      taskDebounce.clearDebouncedValue();
    }
  }, [isClickUpConnected, taskDebounce, setFieldValueRef]);

  return {
    selectedProject,
    isTracking,
    currentTimelogId,

    previousDescription,

    projectOptions,
    taskOptions,
    activeTimelog,
    userProfile,
    formattedTime,
    specificTask,

    projectsLoading,
    tasksLoading,
    activeTimelogLoading,
    isFetchingNextPage,
    specificTaskLoading,

    hasNextPage,
    fetchNextPage,

    tasksError,
    taskErrorDetails,
    retryTasks,

    isClickUpConnected,

    startTimerMutation,
    stopTimerMutation,
    updateTimelogMutation,

    projectDebounce,
    taskDebounce,

    handleFieldUpdate,
    handleStartTimer,
    handleStopTimer,
    handleProjectSelect,
  };
};
