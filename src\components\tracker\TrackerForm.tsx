import React from "react";
import { Box, Button, IconButton, Paper, TextField, Typography, CircularProgress, Tooltip } from "@mui/material";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import ScheduleIcon from "@mui/icons-material/Schedule";
import { useSelector } from "react-redux";
import { Formik, Form } from "formik";
import * as yup from "yup";
import TaskInput from "./taskInput";
import FieldBox from "@/components/common/field-box";
import { RootState } from "@/store/store";
import { useTrackerState } from "@/hooks/tracker/useTrackerState";
import { FormValues } from "@/types/tracker";
import { TrackerFormHandle, TrackerFormProps } from "@/interfaces/tracker.interface";

const TrackerForm = React.forwardRef<TrackerFormHandle, TrackerFormProps>(({ onTrackerStateChange }, ref) => {
  const user_id = useSelector((state: RootState) => state.auth.user?.id);
  const formikValuesRef = React.useRef<FormValues | null>(null);
  const formikSetFieldValueRef = React.useRef<((field: string, value: string | boolean) => void) | null>(null);
  const formikSubmitFormRef = React.useRef<(() => void) | null>(null);

  const memoizedOnTrackerStateChange = React.useCallback(
    (isTracking: boolean) => {
      onTrackerStateChange?.(isTracking);
    },
    [onTrackerStateChange]
  );

  const {
    selectedProject,
    isTracking,
    currentTimelogId,
    previousDescription,
    projectOptions,
    taskOptions,
    activeTimelog,
    formattedTime,
    projectsLoading,
    tasksLoading,
    activeTimelogLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    tasksError,
    taskErrorDetails,
    retryTasks,
    isClickUpConnected,
    startTimerMutation,
    stopTimerMutation,
    projectDebounce,
    taskDebounce,
    handleFieldUpdate,
    handleStartTimer,
    handleStopTimer,
    handleProjectSelect,
    specificTask,
  } = useTrackerState({
    userId: user_id,
    onTrackerStateChange: memoizedOnTrackerStateChange,
    setFieldValueRef: formikSetFieldValueRef,
  });

  React.useImperativeHandle(
    ref,
    () => ({
      restartWithValues: async (values: FormValues) => {
        if (isTracking) {
          throw new Error("Cannot restart timer while another timer is running");
        }

        if (formikSetFieldValueRef.current) {
          Object.entries(values).forEach(([key, value]) => {
            formikSetFieldValueRef.current?.(key, value);
          });

          const project = projectOptions.find(p => p.value === values.project);
          if (project) {
            handleProjectSelect(project);
          }
        }

        await new Promise(resolve => setTimeout(resolve, 100));
        if (formikSubmitFormRef.current) {
          formikSubmitFormRef.current();
        }
      },
    }),
    [isTracking, projectOptions, handleProjectSelect]
  );

  const validationSchema = yup.object({
    project: yup.string().required("Project is required"),
    description: yup
      .string()
      .required("Description is required")
      .max(500, "Description must be less than 500 characters"),
    clickupTaskName: yup.string(),
    isBillable: yup.boolean().required(),
    isOT: yup.boolean().required(),
  });

  const handleBillableToggle = React.useCallback(
    (setFieldValue: (field: string, value: boolean) => void, currentValue: boolean) => {
      const newValue = !currentValue;
      setFieldValue("isBillable", newValue);

      if (isTracking) {
        handleFieldUpdate("isBillable", newValue);
      }
    },
    [isTracking, handleFieldUpdate]
  );

  const handleOvertimeToggle = React.useCallback(
    (setFieldValue: (field: string, value: boolean) => void, currentValue: boolean) => {
      const newValue = !currentValue;
      setFieldValue("isOT", newValue);

      if (isTracking) {
        handleFieldUpdate("isOT", newValue);
      }
    },
    [isTracking, handleFieldUpdate]
  );

  return (
    <Formik
      initialValues={{
        project: activeTimelog?.project || "",
        clickupTaskName: activeTimelog?.clickup_task_id || "",
        description: activeTimelog?.description || "",
        isBillable: activeTimelog?.is_billable ?? true,
        isOT: activeTimelog?.is_ot ?? false,
      }}
      enableReinitialize={true}
      validationSchema={validationSchema}
      validateOnChange={false}
      validateOnBlur={true}
      onSubmit={handleStartTimer}
    >
      {({ values, handleChange, handleBlur, errors, touched, resetForm, setFieldValue, submitForm }) => {
        formikSetFieldValueRef.current = setFieldValue;
        formikValuesRef.current = values;
        formikSubmitFormRef.current = submitForm;

        return (
          <Form style={{ width: "100%" }}>
            <Paper
              elevation={3}
              sx={{
                p: 2,
                display: "flex",
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "space-between",
                gap: 2,
                width: "100%",
              }}
            >
              <FieldBox flex={1}>
                <TaskInput
                  inputOptions={projectOptions}
                  name="project"
                  onInputChange={projectDebounce.setDebouncedValue}
                  onSelectProject={handleProjectSelect}
                  disabled={projectsLoading}
                  onFieldUpdate={handleFieldUpdate}
                />
              </FieldBox>

              {isClickUpConnected &&
                ((selectedProject?.space_id && selectedProject.space_id.trim() !== "") ||
                  (activeTimelog && isTracking && activeTimelog.clickup_task_id)) && (
                  <FieldBox flex={1}>
                    <TaskInput
                      inputOptions={taskOptions}
                      name="clickupTaskName"
                      onInputChange={taskDebounce.setDebouncedValue}
                      label="ClickUp Tasks"
                      disabled={!selectedProject || tasksLoading}
                      hasNextPage={hasNextPage}
                      isFetchingNextPage={isFetchingNextPage}
                      onLoadMore={fetchNextPage}
                      error={tasksError}
                      errorMessage={
                        tasksError
                          ? typeof taskErrorDetails === "object" && taskErrorDetails && "message" in taskErrorDetails
                            ? String(taskErrorDetails.message)
                            : "Failed to load ClickUp tasks"
                          : undefined
                      }
                      onRetry={retryTasks}
                      onFieldUpdate={handleFieldUpdate}
                      specificTask={specificTask}
                    />
                  </FieldBox>
                )}

              <FieldBox
                flex={
                  isClickUpConnected &&
                  ((selectedProject?.space_id && selectedProject.space_id.trim() !== "") ||
                    (activeTimelog && isTracking && activeTimelog.clickup_task_id))
                    ? 3
                    : 4
                }
              >
                <TextField
                  name="description"
                  label="Description"
                  variant="outlined"
                  fullWidth
                  value={values.description}
                  onChange={handleChange}
                  onBlur={e => {
                    handleBlur(e);

                    if (isTracking && e.target.value !== previousDescription.current) {
                      handleFieldUpdate("description", e.target.value);
                      previousDescription.current = e.target.value;
                    }
                  }}
                  onFocus={e => {
                    previousDescription.current = e.target.value;
                  }}
                  error={touched.description && Boolean(errors.description)}
                  inputProps={{
                    maxLength: 500,
                  }}
                  aria-invalid={touched.description && Boolean(errors.description)}
                  aria-describedby={
                    touched.description && Boolean(errors.description) ? "description-error" : undefined
                  }
                  sx={{
                    "& .MuiInputLabel-root": {
                      color: "text.primary",
                    },
                    "& .MuiInputLabel-root.Mui-focused": {
                      color: "text.primary",
                    },
                    "& .MuiOutlinedInput-root": {
                      boxSizing: "border-box",
                    },
                  }}
                />
              </FieldBox>

              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "40px",
                }}
              >
                <Tooltip title={values.isBillable ? "Billable" : "Not Billable"}>
                  <IconButton
                    color={values.isBillable ? "primary" : "default"}
                    onClick={() => handleBillableToggle(setFieldValue, values.isBillable)}
                    aria-label="Toggle Billable"
                    aria-pressed={values.isBillable}
                  >
                    <AttachMoneyIcon />
                  </IconButton>
                </Tooltip>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "40px",
                }}
              >
                <Tooltip title={values.isOT ? "Overtime" : "Regular Time"}>
                  <IconButton
                    color={values.isOT ? "primary" : "default"}
                    onClick={() => handleOvertimeToggle(setFieldValue, values.isOT)}
                    aria-label="Toggle Overtime"
                    aria-pressed={values.isOT}
                  >
                    <ScheduleIcon />
                  </IconButton>
                </Tooltip>
              </Box>

              <Box
                sx={{
                  minWidth: "100px",
                  textAlign: "center",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Typography aria-live="polite">{formattedTime}</Typography>
              </Box>
              <Button
                color={isTracking ? "error" : "primary"}
                variant="contained"
                type={isTracking ? "button" : "submit"}
                onClick={isTracking ? () => handleStopTimer(resetForm) : undefined}
                disabled={
                  activeTimelogLoading ||
                  startTimerMutation.isPending ||
                  stopTimerMutation.isPending ||
                  (isTracking && !currentTimelogId)
                }
              >
                {startTimerMutation.isPending || stopTimerMutation.isPending ? (
                  <CircularProgress size="1em" color="inherit" />
                ) : isTracking ? (
                  "Stop"
                ) : (
                  "Start"
                )}
              </Button>
            </Paper>
          </Form>
        );
      }}
    </Formik>
  );
});

TrackerForm.displayName = "TrackerForm";

export default TrackerForm;
