import React from "react";
import { useProjects } from "@/hooks/tracker/useProjects";
import { ProjectDisplayProps } from "@/types/reports";
import { useMemo } from "react";

const ProjectDisplay: React.FC<ProjectDisplayProps> = ({ projectId }) => {
  const { data } = useProjects("", { ids: projectId ? [projectId] : [] });
  const projectName = useMemo(() => {
    if (!projectId) return "No Project Selected";
    if (!data?.pages) return "Loading...";
    const project = data.pages.flatMap(page => page.results).find(p => p.id === projectId);
    return project?.name || "Unknown Project";
  }, [data, projectId]);

  return <>{projectName}</>;
};

export default ProjectDisplay;
