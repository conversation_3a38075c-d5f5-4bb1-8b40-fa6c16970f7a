import { useState, useMemo } from "react";
import { debounce } from "@mui/material";

export const useDebounce = (delay: number = 300) => {
  const [debouncedValue, setDebouncedValue] = useState("");

  const debouncedSetValue = useMemo(
    () =>
      debounce((value: string) => {
        setDebouncedValue(value);
      }, delay),
    [delay]
  );

  return {
    debouncedValue,
    setDebouncedValue: debouncedSetValue,
    clearDebouncedValue: () => setDebouncedValue(""),
  };
};
