import { Button, Skeleton, Typography } from "@mui/material";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Resource from "@/core/api/resource";
import { ErrorResponse, useNavigate, useParams } from "react-router-dom";
import { Client, Project, statusLabels } from "@/interfaces/project.interface";
import ProjectMembersComponent from "@/screens/project/project.members.screen";
import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb";
import { IActiveBreadcrumbProps } from "@/interfaces/activeBreadcrumbProps.interface";
import ToastMessage from "@/components/toast/toast.component";
import { useQuery } from "@tanstack/react-query";
import theme from "@/theme/theme";
import { useState } from "react";

const ProjectViewScreen: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  if (!id) {
    throw new Error("Project ID is required.");
  }

  const navigate = useNavigate();
  const projectResource = new Resource("projects");
  const clientResource = new Resource("clients");

  const [is404, setIs404] = useState(false);

  const {
    data: project,
    isLoading: isProjectLoading,
    error: projectError,
  } = useQuery<Project, ErrorResponse>({
    queryKey: ["project", id],
    queryFn: async () => {
      const response = await projectResource.get(id);
      return response.data;
    },
    enabled: !!id,
  });

  // Handle project error
  if (projectError) {
    setIs404(true);
    ToastMessage.error("Failed to fetch project");
  }

  const {
    data: client,
    isLoading: clientLoading,
    error: clientError,
  } = useQuery<Client, ErrorResponse>({
    queryKey: ["client", project?.client],
    queryFn: async () => {
      const response = await clientResource.get(project!.client);
      return response.data as Client;
    },
    enabled: !!project?.client, // Only fetch when client exists
  });

  // Handle client error
  if (clientError) {
    ToastMessage.error("Failed to fetch client");
  }

  const trackerPageBreadCrumbs: IActiveBreadcrumbProps = {
    color: theme.palette.primary.main,
    links: [
      {
        text: "Projects",
        link: "/projects",
        customColor: theme.palette.text.primary,
      },
      {
        text: project?.name ?? "",
        link: `/projects/${project?.id ?? ""}`,
        customColor: theme.palette.text.primary,
      },
    ],
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 6,
        p: 3,
      }}
    >
      {is404 ? (
        <Box textAlign="center" py={10} px={2} display="flex" flexDirection="column" alignItems="center">
          <Typography variant="h3" fontWeight="bold" gutterBottom>
            404
          </Typography>
          <Typography variant="h5" gutterBottom>
            Project Not Found
          </Typography>
          <Typography variant="body1" color="text.secondary" mb={3}>
            The project you’re looking for doesn’t exist or was removed.
          </Typography>
          <Button variant="contained" onClick={() => navigate("/projects")}>
            Go to Projects
          </Button>
        </Box>
      ) : (
        <>
          <Box>
            <ActiveBreadcrumb {...trackerPageBreadCrumbs}></ActiveBreadcrumb>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h1">Project Details</Typography>
            </Box>
          </Box>
          <Box>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 6,
              }}
            >
              <Box
                sx={{
                  maxWidth: "100vw",
                  margin: "left auto",
                  minHeight: "70vh",
                }}
              >
                <Grid container spacing={2}>
                  <Grid size={3}>
                    <Typography variant="h4">Name</Typography>
                    {isProjectLoading ? (
                      <Skeleton variant="text" sx={{ fontSize: "1rem", width: "100px" }} />
                    ) : (
                      <Typography variant="body1">{project?.name || "-"}</Typography>
                    )}
                  </Grid>
                  <Grid size={3}>
                    <Typography variant="h4">Client</Typography>
                    {clientLoading || isProjectLoading ? (
                      <Skeleton variant="text" sx={{ fontSize: "1rem", width: "100px" }} />
                    ) : (
                      <Typography variant="body1">{client?.name || "-"}</Typography>
                    )}
                  </Grid>
                  <Grid size={3}>
                    <Typography variant="h4">Privacy</Typography>
                    {isProjectLoading ? (
                      <Skeleton variant="text" sx={{ fontSize: "1rem", width: "100px" }} />
                    ) : (
                      <Typography variant="body1">{project?.is_public ? "Public" : "Private"}</Typography>
                    )}
                  </Grid>
                  <Grid size={3}>
                    <Typography variant="h4">Status</Typography>
                    {isProjectLoading ? (
                      <Skeleton variant="text" sx={{ fontSize: "1rem", width: "100px" }} />
                    ) : (
                      <Typography variant="body1">
                        {statusLabels[project?.status as keyof typeof statusLabels] || "-"}
                      </Typography>
                    )}
                  </Grid>
                  <Grid size={3}>
                    <Typography variant="h4">Type</Typography>
                    {isProjectLoading ? (
                      <Skeleton variant="text" sx={{ fontSize: "1rem", width: "100px" }} />
                    ) : (
                      <Typography variant="body1">{project?.project_type.name || "-"}</Typography>
                    )}
                  </Grid>
                  <Grid size={3}>
                    <Typography variant="h4">Clickup Space</Typography>
                    {isProjectLoading ? (
                      <Skeleton variant="text" sx={{ fontSize: "1rem", width: "100px" }} />
                    ) : (
                      <Typography variant="body1">{project?.clickup_space_id || "-"}</Typography>
                    )}
                  </Grid>
                  <Grid size={3}>
                    <Typography variant="h4">Billable</Typography>
                    {isProjectLoading ? (
                      <Skeleton variant="text" sx={{ fontSize: "1rem", width: "100px" }} />
                    ) : (
                      <Typography variant="body1">{project?.is_billable ? "Yes" : "No"}</Typography>
                    )}
                  </Grid>
                </Grid>
                <Grid container spacing={2}>
                  <Grid size={12}>{project?.id && <ProjectMembersComponent project_id={project.id} />}</Grid>
                </Grid>
              </Box>
            </Box>
          </Box>
        </>
      )}
    </Box>
  );
};
export default ProjectViewScreen;
