import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { User } from "@/interfaces/profile.interface";

export const useUserProfile = () => {
  const userResource = useMemo(() => new Resource("users/my-profile"), []);

  return useQuery<User, Error>({
    queryKey: ["userProfile"],
    queryFn: async () => {
      const response = await userResource.list();
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
};
