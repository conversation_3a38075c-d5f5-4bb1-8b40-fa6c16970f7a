import React from "react";
import { <PERSON><PERSON><PERSON>, Grid, IconButton, Box, Button } from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import { DetailNode } from "@/utils/employee-profile-form-utils";
import { validateSingleField } from "@/utils/field-validation-helpers";

interface EducationSectionProps {
  sectionIndex: number;
  section: DetailNode;
  setFieldValue: (field: string, value: unknown) => void;
}

const EducationSection: React.FC<EducationSectionProps> = ({ sectionIndex, section, setFieldValue }) => {
  const addEducation = () => {
    const currentEducation = section.children || [];
    const newEducation = [
      ...currentEducation,
      {
        value: "",
        children: [{ value: "" }], // degree
      },
    ];
    setFieldValue(`details[${sectionIndex}].children`, newEducation);
  };

  const removeEducation = (index: number) => {
    const currentEducation = section.children || [];
    const newEducation = currentEducation.filter((_, i) => i !== index);
    setFieldValue(`details[${sectionIndex}].children`, newEducation);
  };

  const updateEducation = (index: number, field: string, value: string) => {
    const currentEducation = section.children || [];
    const newEducation = [...currentEducation];
    const education = newEducation[index];

    if (!education) return;

    if (field === "institution") {
      education.value = value;
    } else if (field === "degree") {
      if (!education.children) education.children = [];
      education.children[0] = { value };
    } else if (field === "fieldOfStudy") {
      if (!education.children) education.children = [];
      if (education.children.length < 2) education.children.push({ value });
      else education.children[1] = { value };
    } else if (field === "graduationYear") {
      if (!education.children) education.children = [];
      if (education.children.length < 3) {
        while (education.children.length < 2) education.children.push({ value: "" });
        education.children.push({ value });
      } else {
        education.children[2] = { value };
      }
    }

    setFieldValue(`details[${sectionIndex}].children`, newEducation);
  };

  return (
    <Box>
      {section.children?.map((education, index) => (
        <Box key={index} sx={{ mb: 3, p: 2, border: "1px solid #e0e0e0", borderRadius: 1 }}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Institution"
                value={education.value || ""}
                onChange={e => updateEducation(index, "institution", e.target.value)}
                error={!!validateSingleField("institution", education.value || "")}
                helperText={validateSingleField("institution", education.value || "")}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Degree"
                value={education.children?.[0]?.value || ""}
                onChange={e => updateEducation(index, "degree", e.target.value)}
                error={!!validateSingleField("degree", education.children?.[0]?.value || "")}
                helperText={validateSingleField("degree", education.children?.[0]?.value || "")}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Field of Study (Optional)"
                value={education.children?.[1]?.value || ""}
                onChange={e => updateEducation(index, "fieldOfStudy", e.target.value)}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 5 }}>
              <TextField
                fullWidth
                label="Graduation Year (Optional)"
                type="number"
                value={education.children?.[2]?.value || ""}
                onChange={e => updateEducation(index, "graduationYear", e.target.value)}
                error={!!validateSingleField("graduationYear", education.children?.[2]?.value || "")}
                helperText={validateSingleField("graduationYear", education.children?.[2]?.value || "")}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 1 }}>
              <IconButton onClick={() => removeEducation(index)} color="error">
                <RemoveCircleOutlineIcon />
              </IconButton>
            </Grid>
          </Grid>
        </Box>
      ))}
      <Button startIcon={<AddCircleOutlineIcon />} onClick={addEducation} variant="outlined" size="small">
        Add Education
      </Button>
    </Box>
  );
};

export default EducationSection;
