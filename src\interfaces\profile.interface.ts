export interface User {
  id: string;
  name: string;
  contact: string;
  email?: string;
  status: boolean;
  avatar?: string;
  role: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  groups: IGroup[];
  group_permissions: IGroupPermission[];
  is_invited: boolean;
  detail: IUserDetail;
  connected_service: IConnnectedService[];
}

export interface IUserDetail {
  id: string;
  address: string | null;
  country: string | null;
  country_display: string | null;
  phone: string | null;
  image: string | null;
  user: string;
  timezone?: string | null;
}

export interface IStateData {
  user_id: string;
  schema_name: string;
  service_name: string;
}

export interface IGroup {
  id: number;
  name: string;
  name_display: string;
}

export interface IGroupPermission {
  codename: string;
  name: string;
  app_label: string;
}

export interface IConnnectedService {
  id: string;
  is_connected: boolean;
  expire?: string;
  type: IConnectedType;
}

export interface IConnectedType {
  id: string;
  name: string;
  is_enable: boolean;
}

export interface UserDetail {
  id: string;
  user: string;
  phone?: string;
  address?: string;
  city?: string;
  image?: string;
  country?: string;
  country_display?: string;
  timezone?: string;
}

export interface Group {
  id: number;
  name: string;
  name_display: string;
}

export interface ErrorResponse {
  response?: {
    data?: {
      token?: string[];
      detail?: string;
      error?: string;
    };
  };
}
