import {
  Avatar,
  Box,
  Button,
  Paper,
  Typography,
  MenuItem,
  Select,
  IconButton,
  InputBase,
  Switch,
  FormControl,
  FormGroup,
  FormControlLabel,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import * as Yup from "yup";
import { useState } from "react";
import GenericModal from "@/components/modal/generic-modal.component";
import TableComponent from "@/components/table/table.component";
import { keepPreviousData, useMutation, useQuery } from "@tanstack/react-query";
import TeamAPI from "@/core/api/teams.service";
import {
  GridColDef,
  GridFilterModel,
  GridPaginationModel,
  GridRenderCellParams,
  GridSortModel,
} from "@mui/x-data-grid";
import TeamMemberForm from "@/screens/teams/team-member-form.screen";
import { Formik } from "formik";
import Resource from "@/core/api/resource";
import { Group, IEditUserPayload, ITeam, IUserForm, IUserInvitePayload } from "@/interfaces/teams.interface";
import ToastMessage from "@/components/toast/toast.component";
import { AxiosError, AxiosResponse } from "axios";
import TruncatedText from "@/components/truncatetext/truncatedText";
import { IReinviteErrorResponse, IUserReinvitePayload, MappedTeamMember, statusLabels } from "@/types/teams";
import { GridFeatureMode } from "@/enums/enum.ts";
import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb.tsx";
import { getFormattedErrorMessage } from "@/utils/formatted-error-message";

const toastError = (error: AxiosError) => {
  const message = getFormattedErrorMessage(error.response?.data);
  ToastMessage.error(message);
};

export const TeamMembers = () => {
  const teamResources = new TeamAPI.TeamResources();
  const userInviteResource = new Resource("users/invite");
  const userResource = new Resource("users");
  const userReinviteResource = new Resource("users/reinvite");

  const [filter, setFilter] = useState({
    page: 1,
    pageSize: 10,
    ordering: "first_name",
    search: "",
    status: "all",
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUserData, setSelectedUserData] = useState<ITeam | null>(null);

  const initialValues = {
    first_name: selectedUserData?.first_name || "",
    middle_name: selectedUserData?.middle_name || "",
    last_name: selectedUserData?.last_name || "",
    email: selectedUserData?.email || "",
    groups: selectedUserData?.groups || [],
    country: selectedUserData?.country || [],
    phone: selectedUserData?.phone || [],
    timezone: selectedUserData?.timezone || "",
    is_active: selectedUserData?.is_active,
  };

  const validationSchema = Yup.object().shape({
    first_name: Yup.string().trim().required("First name is required"),
    middle_name: Yup.string().trim().optional(),
    last_name: Yup.string().trim().required("Last name is required"),
    email: Yup.string().trim().email("Invalid email").required("Email is required"),
    groups: Yup.array().min(1, "At least one group must be selected").required("Group is required"),
    country: Yup.string().trim().required("Country is required"),
    phone: Yup.number().required("Phone is required"),
    timezone: Yup.string().required("Timezone is required"),
  });

  const query = {
    page: filter.page,
    "page-size": filter.pageSize,
    ordering: filter.ordering,
    search: filter.search,
    status: filter.status,
  };

  const {
    data: membersList,
    isLoading: membersLoading,
    refetch: refetchMemberList,
  } = useQuery({
    queryKey: ["userTeams", query],
    queryFn: async () => {
      const query: Record<string, unknown> = {
        page: filter.page,
        "page-size": filter.pageSize,
        ordering: filter.ordering,
        search: filter.search,
      };

      if (filter.status === "active") query.is_active = true;
      else if (filter.status === "inactive") query.is_active = false;
      else if (filter.status === "invited") query.is_invited = true;

      const response = await teamResources.list(query);
      return {
        rows: response.data.results,
        count: response.data.count,
      };
    },
    placeholderData: keepPreviousData,
  });

  const handleRowEdit = async (data: ITeam) => {
    setSelectedUserData(data);
    setIsModalOpen(true);
    await refetchMemberList();
    return data;
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilter({
      ...filter,
      search: event.target.value,
    });
  };

  const handleFilterTable = (model: GridFilterModel) => {
    return model;
  };

  const handleSortTable = (model: GridSortModel) => {
    const sortField = model[0]?.field || "";
    const sortDirection = model[0]?.sort || "";

    if (sortField && sortDirection) {
      setFilter(prev => ({
        ...prev,
        ordering: `${sortDirection === "desc" ? "-" : ""}${sortField}`,
      }));
    }
    return model;
  };

  let rows: MappedTeamMember[] =
    membersList?.rows.map((user: ITeam) => ({
      id: user.id,
      first_name: user.first_name,
      middle_name: user.middle_name,
      last_name: user.last_name,
      name: `${user.first_name}  ${user?.middle_name ? user.middle_name : ""} ${user.last_name}`,
      email: user.email,
      is_active: user.is_active,
      is_invited: user.is_invited,
      groups: user.groups,
      profile_image: user.detail?.image,
      phone: user.detail?.phone,
      country: user.detail?.country,
      timezone: user.detail?.timezone,
    })) || [];

  if (filter.status === "active") {
    rows = rows.filter((row: MappedTeamMember) => row.is_active);
  } else if (filter.status === "inactive") {
    rows = rows.filter((row: MappedTeamMember) => !row.is_active);
  } else if (filter.status === "invited") {
    rows = rows.filter((row: MappedTeamMember) => row.is_invited);
  }

  const columns: GridColDef<ITeam>[] = [
    {
      field: "name",
      headerName: "Name",
      sortable: false,
      filterable: false,
      flex: 2,
      renderCell: (params: GridRenderCellParams<ITeam>) => {
        const { name, profile_image } = params.row;
        return (
          <>
            <Avatar alt={name} src={profile_image} sx={{ width: 24, height: 24 }} />
            <Box sx={{ textTransform: "capitalize", mx: 1 }}>
              <TruncatedText text={name} />
            </Box>
          </>
        );
      },
    },
    {
      field: "email",
      headerName: "Email",
      flex: 2,
      sortable: true,
      filterable: true,
    },
    {
      field: "groups",
      headerName: "Groups",
      flex: 2,
      renderCell: (params: GridRenderCellParams<ITeam, Group[]>) => params.value?.map(g => g.name_display).join(", "),
    },
    {
      field: "groupStatus",
      headerName: "Member Status",
      flex: 2,
      renderCell: (params: GridRenderCellParams<ITeam>) => {
        const isActive = params.row.is_active;

        const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
          const updatedValue = event.target.checked;

          const payload: Partial<IEditUserPayload> = {
            id: params.row.id,
            is_active: updatedValue,
          };
          userUpdateMutation.mutate(payload);
        };

        return (
          <FormControl component="fieldset" variant="standard">
            <FormGroup>
              <FormControlLabel
                control={<Switch checked={isActive} onChange={handleToggle} name={`switch-${params.row.id}`} />}
                label=""
              />
            </FormGroup>
          </FormControl>
        );
      },
    },
  ];

  const handlePaginationChange = (model: GridPaginationModel) => {
    setFilter(prev => ({
      ...prev,
      page: model.page + 1,
      pageSize: model.pageSize,
    }));
  };

  const handleClose = () => {
    setIsModalOpen(false);
  };

  const handleCreateUser = () => {
    setIsModalOpen(true);
    setSelectedUserData(null);
  };

  const handleUserReInvite = (data: ITeam) => {
    handleUserReInviteMutation.mutate({ user_id: data.id });
  };

  const userInviteMutation = useMutation({
    mutationFn: (data: IUserInvitePayload) => userInviteResource.store(data),
    onSuccess: async () => {
      setIsModalOpen(false);
      ToastMessage.success("User created successfully");
      await refetchMemberList();
    },
    onError: toastError,
  });

  const handleUserReInviteMutation = useMutation({
    mutationFn: (payload: IUserReinvitePayload) => userReinviteResource.store(payload),
    onSuccess: (res: AxiosResponse) => {
      if (res && res.data && res.data.success) {
        ToastMessage.success(res.data.success);
      } else {
        ToastMessage.success("Reinvite email sent successfully.");
      }
    },
    onError: (error: AxiosError) => {
      if (error.response && error.response.data) {
        const errorData: IReinviteErrorResponse = error.response.data;
        if (errorData.error && Array.isArray(errorData.error) && errorData.error.length > 0) {
          ToastMessage.error(errorData.error[0] || "An unexpected error occurred.");
        } else if (errorData.message) {
          ToastMessage.error(errorData.message);
        } else {
          ToastMessage.error("An unexpected error occurred.");
        }
      } else {
        ToastMessage.error(error.message);
      }
    },
  });

  const userUpdateMutation = useMutation({
    mutationFn: (payload: Partial<IEditUserPayload>) => {
      if (!payload.id) {
        throw new Error("User ID is required for update");
      }
      return userResource.patch(payload.id, payload);
    },
    onSuccess: async () => {
      setIsModalOpen(false);
      await refetchMemberList();
      setSelectedUserData(null);
      ToastMessage.success("User Updated successfully");
    },
    onError: toastError,
  });

  const handelSubmit = async (values: IUserForm) => {
    if (!selectedUserData) {
      const payloadDataForCreate: IUserInvitePayload = {
        first_name: values.first_name,
        last_name: values.last_name,
        middle_name: values.middle_name,
        email: values.email,
        phone: values.phone,
        country: values.country,
        timezone: values.timezone,
        groups: values.groups.map((g: Group) => Number(g.id)),
      };
      userInviteMutation.mutate(payloadDataForCreate);
    } else {
      const payloadDataForEdit: IEditUserPayload = {
        id: selectedUserData.id,
        first_name: values.first_name,
        last_name: values.last_name,
        middle_name: values.middle_name,
        email: values.email,
        group_ids: values.groups.map(g => Number(typeof g === "object" && g !== null ? g.id : g)),
        phone: values.phone,
        country: values.country,
        timezone: values.timezone,
        // is_active: !!values.is_active,
      };
      userUpdateMutation.mutate(payloadDataForEdit);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <ActiveBreadcrumb
            color="inherit"
            links={[
              { text: "Tracker", link: "/tracker" },
              { text: "Teams", link: "/teams/members" },
              { text: "Members", link: "/teams/members" },
            ]}
          />
        </Box>
      </Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" fontWeight={600}>
          Members
        </Typography>
        <Button onClick={handleCreateUser} variant="contained" color="primary">
          + Create User
        </Button>
      </Box>
      <Box
        sx={{
          p: 0,
          mb: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
          flexWrap: "nowrap",
        }}
      >
        <Paper component="div">
          <Select
            value={filter.status}
            onChange={e =>
              setFilter(prev => ({
                ...prev,
                status: e.target.value,
              }))
            }
            displayEmpty
            size="small"
            fullWidth
            sx={{
              height: "100%",
              minWidth: 140,
              "& .MuiSelect-select": {
                display: "flex",
                alignItems: "center",
                height: "100%",
                boxSizing: "border-box",
              },
            }}
          >
            <MenuItem value="all">
              <em>All Status</em>
            </MenuItem>
            {Object.entries(statusLabels).map(([value, label]) => (
              <MenuItem key={value} value={value}>
                {label}
              </MenuItem>
            ))}
          </Select>
        </Paper>

        <Paper
          component="form"
          sx={{
            p: "2px 4px",
            display: "flex",
            alignItems: "center",
            flex: 1,
            height: 40,
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search..."
            inputProps={{ "aria-label": "search" }}
            value={filter.search}
            onChange={handleSearchChange}
          />
          <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
            <SearchIcon />
          </IconButton>
        </Paper>
      </Box>
      <TableComponent
        contentAlign="left"
        columns={columns}
        rows={rows}
        rowCount={membersList?.count}
        loading={membersLoading}
        hideFooter={false}
        paginationMode={GridFeatureMode.SERVER}
        sortingMode={GridFeatureMode.SERVER}
        onPaginationChange={handlePaginationChange}
        onFilterChange={model => {
          handleFilterTable(model);
        }}
        onSortChange={model => {
          handleSortTable(model);
        }}
        actions={[
          {
            label: "Edit",
            handler: data => handleRowEdit(data as ITeam),
          },
          {
            label: "Reinvite",
            handler: data => handleUserReInvite(data as ITeam),
            shouldShow: row => row.is_invited,
          },
        ]}
      />

      <GenericModal
        setting={{ open: isModalOpen, onClose: handleClose }}
        title={selectedUserData ? "Edit User" : "Create User"}
        height="500px"
      >
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={values => handelSubmit(values as IUserForm)}
        >
          {formikProps => (
            <form onSubmit={formikProps.handleSubmit}>
              <TeamMemberForm openEdit={isModalOpen} selectedData={selectedUserData} />
            </form>
          )}
        </Formik>
      </GenericModal>
    </Box>
  );
};
