import React from "react";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { FormControl, FormHelperText } from "@mui/material";

interface GenericDateTimePickerProps {
  label: string;
  value: Date | null;
  onChange: (date: Date | null) => void;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  disabled?: boolean;
  variant?: "outlined" | "filled" | "standard";
}

const GenericDateTimePicker: React.FC<GenericDateTimePickerProps> = ({
  label,
  value,
  onChange,
  error = false,
  helperText,
  fullWidth = true,
  disabled = false,
  variant = "outlined",
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <FormControl fullWidth={fullWidth} error={error}>
        <DateTimePicker
          label={label}
          value={value}
          onChange={onChange}
          disabled={disabled}
          slotProps={{
            textField: {
              fullWidth: fullWidth,
              variant: variant,
              error: error,
            },
          }}
        />
        {helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>
    </LocalizationProvider>
  );
};

export default GenericDateTimePicker;
