import TableComponent from "@/components/table/table.component";
import {
  AssignUserPayload,
  Member,
  ProjectMemberProps,
  UpdateUserVariables,
  User,
  UserAssignFormValues,
  ProjectRole,
  UpdateUserPayload,
} from "@/interfaces/project.interface";
import {
  Autocomplete,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormGroup,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import { useMemo, useState } from "react";
import Resource from "@/core/api/resource";
import { useFormik } from "formik";
import * as yup from "yup";
import { useMutation, useQuery, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import { ErrorResponse } from "@/interfaces/table.interface";
import ToastMessage from "@/components/toast/toast.component";
import { ProjectAssignedType } from "@/enums/enum";
import theme from "@/theme/theme";
import { GridColDef } from "@mui/x-data-grid";
import { allSkills } from "@/components/profiles/constants";
import { useUsers } from "@/core/hooks/useUsers";

const assignedTypeLabels: Record<ProjectAssignedType, string> = {
  [ProjectAssignedType.FULLTIME]: "Full Time",
  [ProjectAssignedType.DEDICATED]: "Dedicated",
  [ProjectAssignedType.TEMPSUPPORT]: "Temporary Support",
  [ProjectAssignedType.HOURLY]: "Hourly",
};

const ProjectMembersComponent: React.FC<ProjectMemberProps> = ({ project_id }) => {
  const queryClient = useQueryClient();
  const [openUserAssignModel, setOpenUserAssignModel] = useState(false);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);

  const projectUserResource = new Resource("projects/user");
  const projectRoleResource = new Resource("projects/roles");

  const validationSchema = yup.object().shape({
    assigned_type: yup.string().required("Assigned type is required"),
    selectedUser: yup.object().required("User is required"),
    project_role: yup
      .object({
        id: yup.string().required(),
        name: yup.string().required(),
      })
      .required("Project role is required")
      .nullable(),
  });

  const query = {
    project_id: project_id,
    page_size: 100,
  };

  const {
    data: members,
    isLoading: membersLoading,
    refetch: refetchMembers,
  } = useQuery({
    queryKey: ["projectMembers", query],
    queryFn: async () => {
      const response = await projectUserResource.list(query);
      return response.data.results as Member[];
    },
    placeholderData: keepPreviousData,
  });

  const { data: allUsers } = useUsers();

  const availableUsers = useMemo(() => {
    const users = allUsers ?? []; // fallback empty array if undefined
    const assignedMembers = members ?? [];
    const assignedIds = new Set(assignedMembers.map(m => m.user.id));
    return users.filter(user => user.is_active && !assignedIds.has(user.id));
  }, [allUsers, members]);

  const {
    data: projectRoles,
    isLoading: rolesLoading,
    error: rolesError,
  } = useQuery({
    queryKey: ["projectRoles"],
    queryFn: async () => {
      const response = await projectRoleResource.list();
      return response.data.results as ProjectRole[];
    },
    placeholderData: keepPreviousData,
  });

  const createUserMutation = useMutation({
    mutationFn: (payload: AssignUserPayload) => projectUserResource.store(payload),
    onSuccess: () => {
      ToastMessage.success("User assigned successfully.");
      refetchMembers();
      handleUserAssignModelClose();
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  const updateMemberMutation = useMutation({
    mutationFn: ({ id, payload }: UpdateUserVariables) => projectUserResource.patch(id, payload),
    onSuccess: () => {
      ToastMessage.success("User updated successfully.");
      refetchMembers();
      handleUserAssignModelClose();
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  const removeMemberMutation = useMutation({
    mutationFn: (memberId: string) => projectUserResource.destroy(memberId),
    onSuccess: () => {
      ToastMessage.success("User removed successfully.");
      queryClient.invalidateQueries({
        predicate: query => query.queryKey[0] === "projectMembers",
      });
    },
    onError: (err: unknown) => {
      const error = err as ErrorResponse;
      let errorMessage = "An unexpected error occurred.";
      const errorData = error?.response?.data?.error;
      if (Array.isArray(errorData)) {
        errorMessage = errorData.join(", ");
      } else if (typeof errorData === "string") {
        errorMessage = errorData;
      } else if (error?.message) {
        errorMessage = error.message;
      }
      ToastMessage.error(errorMessage);
    },
  });

  const handleUserAssignModelSubmit = async (values: UserAssignFormValues) => {
    if (!values.selectedUser) {
      ToastMessage.error("Please select a user.");
      return;
    }
    if (!values.project_role) {
      ToastMessage.error("Please select a project role.");
      return;
    }
    const userId = values.selectedUser.id;

    if (selectedMember) {
      const memberId = selectedMember.id;
      const payload: UpdateUserPayload = {
        assigned_type: values.assigned_type,
        tech_stack: values.tech_stack,
        project_role: values.project_role?.id,
      };
      updateMemberMutation.mutate({ id: memberId, payload });
    } else {
      const payload: AssignUserPayload = {
        project: project_id ?? "",
        user: userId,
        assigned_type: values.assigned_type,
        tech_stack: values.tech_stack,
        project_role: values.project_role?.id,
      };
      createUserMutation.mutate(payload);
    }
  };

  const handleMemberEdit = async (member: Member) => {
    setSelectedMember(member);
    handleUserAssignModelOpen();
  };

  const handleUserAssignModelClose = () => {
    setSelectedMember(null);
    formik.resetForm();
    setOpenUserAssignModel(false);
  };

  const handleUserAssignModelOpen = () => {
    setOpenUserAssignModel(true);
  };

  const columns: GridColDef[] = [
    {
      field: "full_name",
      headerName: "Full Name",
      sortable: true,
      filterable: true,
      flex: 2,
      valueGetter: (_value, member: Member) => {
        return `${member.user.first_name || ""} ${member.user.last_name || ""}`.trim();
      },
    },
    {
      field: "email",
      headerName: "Email",
      sortable: true,
      filterable: true,
      flex: 2,
      valueGetter: (_value, member: Member) => {
        return member.user.email || "";
      },
    },
    {
      field: "assigned_type",
      headerName: "Assigned Type",
      sortable: true,
      filterable: true,
      flex: 1,
      valueGetter: (_value, member: Member) => {
        return assignedTypeLabels[member.assigned_type as ProjectAssignedType] || "Unknown";
      },
    },
    {
      field: "tech_stack",
      headerName: "Tech Stack",
      sortable: false,
      filterable: false,
      flex: 2,
      valueGetter: (_value, member: Member) => {
        const techStackNames = member.tech_stack.map(tech => tech.name);
        return techStackNames.join(", ") || "—";
      },
    },
    {
      field: "project_role",
      headerName: "Project Role",
      sortable: true,
      filterable: true,
      flex: 1,
      valueGetter: (_value, member: Member) => {
        if (typeof member.project_role === "string") {
          const role = projectRoles?.find(r => r.id === member.project_role?.id);
          return role?.name || member.project_role || "—";
        }
        return member.project_role?.name || "—"; // Use name if project_role is an object
      },
    },
  ];

  const formik = useFormik<UserAssignFormValues>({
    initialValues: {
      selectedUser: selectedMember?.user ?? null,
      selectedMember: selectedMember ?? null,
      assigned_type: selectedMember?.assigned_type || "",
      tech_stack: selectedMember?.tech_stack?.map(item => item.name) ?? [],
      project_role: selectedMember?.project_role,
    },
    enableReinitialize: true,
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        await handleUserAssignModelSubmit(values);
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <>
      <Dialog open={openUserAssignModel} onClose={handleUserAssignModelClose}>
        <DialogTitle>{selectedMember ? "Edit User Assignment" : "Assign User"}</DialogTitle>
        <form onSubmit={formik.handleSubmit} noValidate>
          <DialogContent sx={{ width: "500px", maxWidth: "100%" }}>
            {selectedMember ? (
              <FormGroup sx={{ mb: 2, width: "100%" }}>
                <TextField
                  fullWidth
                  value={`${selectedMember.user.first_name} ${selectedMember.user.last_name}`}
                  variant="outlined"
                  label="User"
                  disabled
                  InputLabelProps={{
                    sx: {
                      color: "gray",
                      "&.Mui-focused": { color: "gray" },
                    },
                  }}
                />
                <input type="hidden" name="selectedUser" value={formik.values.selectedUser?.id ?? ""} />
              </FormGroup>
            ) : (
              <FormGroup sx={{ mb: 2, width: "100%" }}>
                <Autocomplete
                  disablePortal={false}
                  options={availableUsers || []}
                  getOptionLabel={(user: User) => (user ? `${user.first_name} ${user.last_name}` : "")}
                  isOptionEqualToValue={(user: User, value) => user.id === value.id}
                  value={formik.values.selectedUser}
                  onChange={(_event, newValue) => {
                    formik.setFieldValue("selectedUser", newValue);
                  }}
                  getOptionDisabled={option => {
                    const memberIds = members?.map((m: Member) => m.user.id) || [];
                    return memberIds.includes(option.id);
                  }}
                  ListboxProps={{
                    style: { maxHeight: 5 * 48, overflow: "auto", width: "100%" },
                  }}
                  renderOption={(props, option) => (
                    <li {...props} key={option.id}>
                      {option.first_name} {option.last_name}
                    </li>
                  )}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label="Select User"
                      error={formik.touched.selectedUser && !!formik.errors.selectedUser}
                      helperText={formik.touched.selectedUser && formik.errors.selectedUser}
                      InputLabelProps={{
                        sx: { color: theme.palette.custom.brand.silverChalice },
                      }}
                    />
                  )}
                  sx={{ maxWidth: "100%" }}
                />
              </FormGroup>
            )}

            <FormGroup sx={{ mb: 2, maxWidth: "100%" }}>
              <FormControl fullWidth error={formik.touched.assigned_type && !!formik.errors.assigned_type}>
                <Select
                  name="assigned_type"
                  value={formik.values.assigned_type || ""}
                  onChange={formik.handleChange}
                  displayEmpty
                  renderValue={selected => {
                    if (!selected) {
                      return <span style={{ color: theme.palette.custom.brand.silverChalice }}>Assigned type</span>;
                    }
                    return assignedTypeLabels[selected as ProjectAssignedType] || "Unknown";
                  }}
                  sx={{ maxWidth: "100%" }}
                >
                  <MenuItem value="" disabled>
                    Assigned type
                  </MenuItem>
                  {Object.entries(assignedTypeLabels).map(([value, label]) => (
                    <MenuItem key={value} value={value}>
                      {label}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.assigned_type && formik.errors.assigned_type && (
                  <FormHelperText>{formik.errors.assigned_type}</FormHelperText>
                )}
              </FormControl>
            </FormGroup>

            <FormGroup sx={{ mb: 2, maxWidth: "100%" }}>
              <FormControl fullWidth error={formik.touched.project_role && !!formik.errors.project_role}>
                <Select
                  name="project_role"
                  value={formik.values.project_role?.id || ""}
                  onChange={event => {
                    const selectedId = event.target.value;
                    const selectedRole = projectRoles?.find(r => r.id === selectedId) || null;
                    formik.setFieldValue("project_role", selectedRole);
                  }}
                  displayEmpty
                  renderValue={selected => {
                    if (!selected) {
                      return (
                        <span style={{ color: theme.palette.custom.brand.silverChalice }}>Select project role</span>
                      );
                    }
                    const selectedRole = projectRoles?.find(r => r.id === selected);
                    return selectedRole?.name ?? "Invalid role";
                  }}
                >
                  <MenuItem value="" disabled>
                    Select project role
                  </MenuItem>
                  {projectRoles?.map(role => (
                    <MenuItem key={role.id} value={role.id}>
                      {role.name}
                    </MenuItem>
                  ))}
                </Select>

                {formik.touched.project_role && formik.errors.project_role && (
                  <FormHelperText>
                    {typeof formik.errors.project_role === "string"
                      ? formik.errors.project_role
                      : "Project role is required"}
                  </FormHelperText>
                )}

                {rolesLoading && <FormHelperText>Loading project roles...</FormHelperText>}
                {!rolesLoading && (!projectRoles || projectRoles.length === 0) && (
                  <FormHelperText>No project roles available</FormHelperText>
                )}
                {rolesError && <FormHelperText error>Failed to load project roles</FormHelperText>}
              </FormControl>
            </FormGroup>

            <FormGroup sx={{ maxWidth: "100%" }}>
              <Autocomplete
                multiple
                options={allSkills}
                value={formik.values.tech_stack}
                onChange={(_event, newValue) => {
                  formik.setFieldValue("tech_stack", newValue);
                }}
                renderTags={(value: string[], getTagProps) => (
                  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                    {value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        key={option}
                        sx={{ flex: "0 1 calc(50% - 4px)", maxWidth: "calc(50% - 4px)" }}
                      />
                    ))}
                  </Box>
                )}
                renderInput={params => (
                  <TextField
                    {...params}
                    label="Tech Stack"
                    placeholder="Select tech stack"
                    error={formik.touched.tech_stack && !!formik.errors.tech_stack}
                    helperText={
                      formik.touched.tech_stack && typeof formik.errors.tech_stack === "string"
                        ? formik.errors.tech_stack
                        : undefined
                    }
                    InputLabelProps={{
                      sx: { color: theme.palette.custom.brand.silverChalice },
                    }}
                  />
                )}
              />
            </FormGroup>
          </DialogContent>

          <DialogActions>
            <Button onClick={handleUserAssignModelClose}>Cancel</Button>
            <Button
              type="submit"
              disabled={formik.isSubmitting || rolesLoading || !projectRoles?.length}
              variant="contained"
            >
              {selectedMember ? "Update" : "Assign"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography sx={{ mt: 4, mb: 2 }} variant="h2" component="div">
            Members
          </Typography>
          <Button variant="contained" onClick={handleUserAssignModelOpen} startIcon={<AddOutlinedIcon />}>
            Assign User
          </Button>
        </Box>
      </Box>
      <TableComponent
        contentAlign="left"
        columns={columns}
        rows={members || []}
        loading={membersLoading}
        hideFooter={true}
        rowCount={0}
        actions={[
          {
            label: "Edit",
            handler: member => {
              handleMemberEdit(member as Member);
            },
          },
          {
            label: "Remove",
            handler: member => {
              removeMemberMutation.mutate(member.id);
            },
          },
        ]}
      />
    </>
  );
};

export default ProjectMembersComponent;
