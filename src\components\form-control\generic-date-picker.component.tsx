import React from "react";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { FormControl, FormHelperText } from "@mui/material";

interface GenericDatePickerProps {
  label: string;
  value: Date | null;
  onChange: (date: Date | null) => void;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  disabled?: boolean;
  variant?: "outlined" | "filled" | "standard";
}

const GenericDatePicker: React.FC<GenericDatePickerProps> = ({
  label,
  value,
  onChange,
  error = false,
  helperText,
  fullWidth = true,
  disabled = false,
  variant = "outlined",
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <FormControl fullWidth={fullWidth} error={error}>
        <DatePicker
          label={label}
          value={value}
          onChange={onChange}
          disabled={disabled}
          slotProps={{
            textField: {
              fullWidth: fullWidth,
              variant: variant,
              error: error,
            },
          }}
        />
        {helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>
    </LocalizationProvider>
  );
};

export default GenericDatePicker;
