export interface Group {
  id: number;
  name: string;
}

export interface IUserReinvitePayload {
  user_id: string;
}

export interface Permission {
  [key: string]: string | number | boolean;
}

export interface IReinviteErrorResponse {
  error?: string[];
  message?: string;
}

export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  groups: Group[];
  permissions: Permission[];
}

export interface PaginatedUserResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: User[];
}
export interface UserListResponse {
  userList: PaginatedUserResponse;
}

export interface MappedTeamMember {
  id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  name: string;
  email: string;
  is_active: boolean;
  is_invited: boolean;
  groups: Group[];
  profile_image: string;
  phone?: string;
  country?: string;
  timezone?: string;
}

export const statusLabels = {
  active: "Active",
  inactive: "Inactive",
  invited: "Invited",
};

export interface Country {
  name: string;
  display_name: string;
}
