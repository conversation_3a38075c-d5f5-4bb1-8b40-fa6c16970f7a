import { toast, ToastOptions } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
export interface CustomToastInterface {
  defaultOptions: ToastOptions;
  success(message: string, options?: ToastOptions): void;
  error(message: string, options?: ToastOptions): void;
  info(message: string, options?: ToastOptions): void;
}

class CustomToast implements CustomToastInterface {
  defaultOptions: ToastOptions;
  constructor() {
    toast.dismiss();
    this.defaultOptions = {
      position: "top-right",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    };
  }

  success(message: string, options?: ToastOptions) {
    toast.success(message, {
      ...this.defaultOptions,
      ...options,
      // style: { backgroundColor: "#64F099" },
    });
  }
  error(message: string, options?: ToastOptions) {
    toast.error(message, {
      ...this.defaultOptions,
      ...options,
      // style: { backgroundColor: "#DF320C" },
    });
  }
  info(message: string, options?: ToastOptions) {
    toast.info(message, { ...this.defaultOptions, ...options });
  }
}

const ToastMessage = new CustomToast();

export default ToastMessage;
