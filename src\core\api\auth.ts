import { ILogin } from "@/interfaces/login.interface";
import {
  AUTH_CHANGE_PASSWORD,
  AUTH_FORGOT_PASSWORD,
  AUTH_LOGIN,
  AUTH_LOGOUT,
  AUTH_REFRESH_TOKEN,
  AUTH_RESET_PASSWORD_CONFIRM,
  AUTH_RESET_PASSWORD_CHECK,
} from "../http/endpoint-urls";
import http from "../http/http";
import Resource from "./resource";
import { dispatchClearUser } from "@/store/manager";
import { AuthResponse } from "@/types";
import { IResetPasswordConfirm } from "@/interfaces/reset-password.interface";

class AuthResource extends Resource {
  constructor() {
    super("auth");
  }

  loginUser(loginUser: ILogin): Promise<AuthResponse> {
    return http({
      url: AUTH_LOGIN,
      method: "post",
      data: loginUser,
      withCredentials: true,
    })
      .then((response) => response.data)
      .catch((error) => {
        throw error;
      });
  }

  forgetPassword(email: string) {
    return http({
      url: AUTH_FORGOT_PASSWORD,
      method: "post",
      data: { email },
    })
      .then((response) => response) // return full response
      .catch((error) => {
        throw error;
      });
  }

  resetPasswordConfirm(data: IResetPasswordConfirm) {
    return http({
      url: AUTH_RESET_PASSWORD_CONFIRM,
      method: "post",
      data,
    })
      .then((response) => response)
      .catch((error) => {
        throw error;
      });
  }

  verifyResetToken({ uid, token }: { uid: string; token: string }) {
    return http({
      url: AUTH_RESET_PASSWORD_CHECK,
      method: "post",
      data: {
        uid,
        token,
      },
    })
      .then((response) => response)
      .catch((error) => {
        throw error;
      });
  }

  changePassword(
    oldPassword: string,
    newPassword: string,
    confirmPassword: string,
  ) {
    if (!confirmPassword) {
      return Promise.reject(new Error("Confirm password is required."));
    }
    return http({
      url: AUTH_CHANGE_PASSWORD,
      method: "post",
      data: {
        old_password: oldPassword,
        new_password: newPassword,
        confirm_password: confirmPassword,
      },
    })
      .then((response) => response.data)
      .catch((error) => {
        throw error;
      });
  }

  logout() {
    return http({
      url: AUTH_LOGOUT,
      method: "post",
    })
      .then((response) => {
        dispatchClearUser();
        return response.data;
      })
      .catch((error) => {
        throw error;
      });
  }
  refreshUser() {
    return http({
      url: AUTH_REFRESH_TOKEN,
      method: "post",
      withCredentials: true,
    })
      .then((response) => response.data)
      .catch((error) => {
        throw error;
      });
  }
}

export { AuthResource as default };
