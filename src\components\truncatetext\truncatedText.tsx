import { Tooltip, Typography } from "@mui/material";

const TruncatedText = ({ text }: { text?: string }) => {
  return (
    <Tooltip title={text}>
      <Typography
        variant="body2"
        sx={{
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
          maxWidth: 200, // Set your desired width
          cursor: "pointer",
        }}
      >
        {text}
      </Typography>
    </Tooltip>
  );
};

export default TruncatedText;
