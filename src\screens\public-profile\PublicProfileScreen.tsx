// import React from "react";
// import { Container, Paper, Typography } from "@mui/material";
// import ProfileSection from "@/components/profiles/profile-section.component.tsx";
// import ProfileHeader from "@/components/profiles/profile-header.component.tsx";
// import WorkExperienceItem from "@/components/profiles/work-experience-item.component.tsx";

// const profileData = {
//   name: "Dip P.",
//   title: "Software Engineer",
//   headerTitle: "Outcode.",
//   candidateBackground: {
//     title: "CANDIDATE BACKGROUND",
//     coreLanguages: "Ruby, JavaScript, TypeScript",
//     coreFramework: "Ruby on Rails, Node.js, React, Next.js",
//     additionalSkills: "RESTful APIs, SQL, AWS, Docker, Github",
//   },
//   languages: {
//     title: "LANGUAGES",
//     list: [
//       { language: "English", proficiency: "Fluent" },
//       { language: "Spanish", proficiency: "Intermediate" },
//       { language: "French", proficiency: "Beginner" },
//     ],
//   },
//   availability: {
//     title: "AVAILABILITY",
//     status: "Immediate",
//   },
//   education: {
//     title: "EDUCATION",
//     university: "Kathmandu University",
//     degree: "Bachelor's in computer engineering",
//   },
//   workExperience: {
//     title: "WORK EXPERIENCE",
//     experiences: [
//       {
//         dateRange: "May 2024 - Jan 2025",
//         company: "Solaresame, San Mateo, USA",
//         role: "Senior Software Engineer",
//         responsibilities: [
//           "Built and maintained scalable Node.js backend applications with high performance.",
//           "Designed AWS-based microservices (SQS, SNS, Lambda) for event-driven workflows.",
//           "Automated sales agent processes, accelerating deal closures.",
//           "Integrated Google Maps and solar APIs for roof area calculations and optimized rendering.",
//           "Handled timezone-aware scheduling (PDT/PST) for agent-customer follow-ups.",
//         ],
//         technologies:
//           "Node.js, AWS (SQS, SNS, Lambda), Google Maps API, JavaScript, REST APIs",
//       },
//     ],
//   },
// };

// const PublicProfileScreen: React.FC = () => {
//   return (
//     <Container
//       maxWidth="lg"
//       sx={{ backgroundColor: "#ffffff", py: { xs: 2, md: 4 } }}
//     >
//       <Paper elevation={3} sx={{ borderRadius: 0 }}>
//         <ProfileHeader
//           headerTitle={profileData.headerTitle}
//           name={profileData.name}
//           title={profileData.title}
//         />

//         <div style={{ padding: "0 16px 32px 16px" }}>
//           <ProfileSection title={profileData.candidateBackground.title}>
//             <Typography variant="body1" gutterBottom>
//               <strong>Core Languages:</strong>{" "}
//               {profileData.candidateBackground.coreLanguages}
//             </Typography>
//             <Typography variant="body1" gutterBottom>
//               <strong>Core Framework:</strong>{" "}
//               {profileData.candidateBackground.coreFramework}
//             </Typography>
//             <Typography variant="body1">
//               <strong>Additional Skills:</strong>{" "}
//               {profileData.candidateBackground.additionalSkills}
//             </Typography>
//           </ProfileSection>{" "}
//           <ProfileSection title={profileData.languages.title}>
//             {profileData.languages.list.map((lang, index) => (
//               <Typography variant="body1" key={index} gutterBottom>
//                 <strong>{lang.language}:</strong> {lang.proficiency}
//               </Typography>
//             ))}
//           </ProfileSection>
//           <ProfileSection title={profileData.availability.title}>
//             <Typography variant="body1">
//               <strong>{profileData.availability.status}</strong>
//             </Typography>
//           </ProfileSection>
//           <ProfileSection title={profileData.education.title}>
//             <Typography variant="body1" sx={{ fontWeight: "bold" }}>
//               {profileData.education.university}
//             </Typography>
//             <Typography variant="body1">
//               {profileData.education.degree}
//             </Typography>
//           </ProfileSection>
//           <ProfileSection title={profileData.workExperience.title}>
//             {profileData.workExperience.experiences.map((exp, index) => (
//               <WorkExperienceItem
//                 key={index}
//                 experience={exp}
//                 isLast={
//                   index === profileData.workExperience.experiences.length - 1
//                 }
//               />
//             ))}
//           </ProfileSection>
//         </div>
//       </Paper>
//     </Container>
//   );
// };

// export default PublicProfileScreen;
