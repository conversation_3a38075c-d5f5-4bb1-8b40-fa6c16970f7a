import React from "react";
import { Box, Typography, List } from "@mui/material";
import { format, parseISO } from "date-fns";
import TimelogEntry from "./EditableTimelogEntry";
import { DailyTimelogGroupProps } from "@/types/tracker";

const DailyTimelogGroup: React.FC<DailyTimelogGroupProps> = React.memo(({ date, logs, onRestartTimer, isTracking }) => {
  const displayDate = React.useMemo(() => {
    try {
      return format(parseISO(date), "EEEE, MMMM d");
    } catch {
      return date;
    }
  }, [date]);

  const totalDuration = React.useMemo(() => {
    const totalSeconds = logs.reduce((total, log) => {
      if (!log.end_time) return total;

      const startTime = new Date(log.start_time);
      const endTime = new Date(log.end_time);
      const duration = Math.max(0, (endTime.getTime() - startTime.getTime()) / 1000);

      return total + duration;
    }, 0);

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }, [logs]);
  return (
    <Box sx={{ mb: { xs: 2, sm: 3 } }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          px: { xs: 1.5, sm: 2 },
          py: { xs: 1, sm: 1.5 },
          borderBottom: 1,
          borderColor: "divider",
          backgroundColor: theme => theme.palette.background.paper,
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 0.5, sm: 0 },
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: 600,
            color: "text.primary",
            fontSize: { xs: "0.9rem", sm: "1rem" },
          }}
        >
          {displayDate}
        </Typography>

        <Typography
          variant="body2"
          sx={{
            fontWeight: 500,
            color: "text.secondary",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
          }}
          aria-label={`Total time for ${displayDate}: ${totalDuration}`}
        >
          Total: {totalDuration}
        </Typography>
      </Box>

      <List disablePadding>
        {logs.map(log => (
          <TimelogEntry key={log.id} log={log} onRestartTimer={onRestartTimer} isTracking={isTracking} />
        ))}
      </List>
    </Box>
  );
});

DailyTimelogGroup.displayName = "DailyTimelogGroup";

export default DailyTimelogGroup;
