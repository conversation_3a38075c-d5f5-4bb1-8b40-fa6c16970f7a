/**
 * Utilities for handling work experience date formatting and parsing
 * Used to convert between backend duration strings and frontend date inputs
 */

export interface ParsedDuration {
  startDate: string; // YYYY-MM format for date inputs
  endDate: string; // YYYY-MM format for date inputs
  isCurrent: boolean;
}

/**
 * Converts a duration string like "Jun 2025 - Present" to separate date values
 * @param durationString - The formatted duration string from backend
 * @returns Object with startDate, endDate, and isCurrent values
 */
export const parseDurationString = (durationString: string): ParsedDuration => {
  if (!durationString) {
    return { startDate: "", endDate: "", isCurrent: false };
  }

  const parts = durationString.split(" - ");
  const startPart = parts[0]?.trim() || "";
  const endPart = parts[1]?.trim() || "";

  const convertToDateInput = (dateStr: string): string => {
    if (!dateStr) return "";
    try {
      // Add day to parse correctly (e.g., "Jun 2025" -> "Jun 2025 01")
      const date = new Date(dateStr + " 01");
      if (isNaN(date.getTime())) return "";

      // Extract year and month directly from local date to avoid timezone issues
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // getMonth() returns 0-11, so add 1
      return `${year}-${month.toString().padStart(2, "0")}`; // Return YYYY-MM format
    } catch {
      return "";
    }
  };

  const startDate = convertToDateInput(startPart);
  const endDate = endPart === "Present" ? "" : convertToDateInput(endPart);
  const isCurrent = endPart === "Present";

  return { startDate, endDate, isCurrent };
};

/**
 * Converts date inputs to a formatted duration string for backend
 * @param startDate - Start date in YYYY-MM format
 * @param endDate - End date in YYYY-MM format (optional)
 * @param isCurrent - Whether this is a current position
 * @returns Formatted duration string like "Jun 2025 - Present"
 */
export const formatDurationString = (
  startDate: string,
  endDate: string,
  isCurrent: boolean,
): string => {
  if (!startDate) return "";

  const formatDateToString = (dateInput: string): string => {
    if (!dateInput) return "";
    try {
      const parts = dateInput.split("-");
      const year = parts[0];
      const month = parts[1];
      if (!year || !month) return dateInput;

      const date = new Date(parseInt(year), parseInt(month) - 1);
      return date.toLocaleDateString("en-US", {
        month: "short",
        year: "numeric",
      });
    } catch {
      return dateInput;
    }
  };

  const startFormatted = formatDateToString(startDate);

  if (isCurrent) {
    return `${startFormatted} - Present`;
  } else if (endDate) {
    const endFormatted = formatDateToString(endDate);
    return `${startFormatted} - ${endFormatted}`;
  } else {
    return startFormatted;
  }
};

/**
 * Formats work experience data for backend consumption
 * @param work - Work experience data from frontend
 * @returns Formatted duration string
 */
export const createWorkExperienceDuration = (work: {
  startDate: string;
  endDate?: string;
  isCurrent?: boolean;
}): string => {
  const startDate = work.startDate ? new Date(work.startDate) : null;
  if (!startDate || isNaN(startDate.getTime())) return "";

  const startFormatted = startDate.toLocaleDateString("en-US", {
    month: "short",
    year: "numeric",
  });

  if (work.isCurrent) {
    return `${startFormatted} - Present`;
  } else if (work.endDate) {
    const endDate = new Date(work.endDate);
    if (!isNaN(endDate.getTime())) {
      const endFormatted = endDate.toLocaleDateString("en-US", {
        month: "short",
        year: "numeric",
      });
      return `${startFormatted} - ${endFormatted}`;
    }
  }

  return startFormatted;
};
