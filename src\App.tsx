import React from "react";
import Router from "@/router/Router";
import { Box } from "@mui/material";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import ErrorBoundary from "./screens/errors/ErrorBoundary";

const queryClient = new QueryClient();

// this file can be skipped by adding router in the main.tsx itself
const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Box sx={{ width: "100vw" }}>
          <Router />
        </Box>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
