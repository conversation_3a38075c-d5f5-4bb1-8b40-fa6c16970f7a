import React, { useEffect, useState } from "react";
import { Box, Typography, Button, Paper, InputBase, IconButton } from "@mui/material";
import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb";
import { IActiveBreadcrumbProps } from "@/interfaces/activeBreadcrumbProps.interface";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import Resource from "@/core/api/resource";
import { ProjectType, ProjectTypeFormData } from "@/interfaces/project.interface";
import { useMutation, useQuery, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import TableComponent from "@/components/table/table.component";
import { GridFilterModel, GridPaginationModel, GridSortModel } from "@mui/x-data-grid";
import { GridFeatureMode } from "@/enums/enum";
import * as yup from "yup";
import ToastMessage from "@/components/toast/toast.component";
import { useFormik } from "formik";
import theme from "@/theme/theme";

import SearchIcon from "@mui/icons-material/Search";
import ProjectTypeForm from "./project-type.form.screen";

const ProjectTypeScreen = () => {
  const queryClient = useQueryClient();

  const projectTypeResource = new Resource("projects/types");
  const projectResource = new Resource("projects/types");

  const [selectedProjectType, setSelectedProjectType] = useState<ProjectType | null>(null);
  const [isCreateEditModelOpen, setIsCreateEditModelOpen] = useState(false);
  const [isDeletePopupOpen, setIsDeletePopupOpen] = React.useState(false);
  const [projectTypeQueryParams, setProjectTypeQueryParams] = useState({
    page: 1,
    pageSize: 10,
    ordering: "updated_at",
    search: "",
  });
  const [searchTerm, setSearchTerm] = useState(projectTypeQueryParams.search);

  useEffect(() => {
    const handler = setTimeout(() => {
      setProjectTypeQueryParams(prev => ({
        ...prev,
        search: searchTerm,
      }));
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  const trackerPageBreadCrumbs: IActiveBreadcrumbProps = {
    color: theme.palette.primary.main,
    links: [
      {
        text: "Projects",
        link: "/projects",
        customColor: theme.palette.text.primary,
      },
      {
        text: "Project Types",
        link: "/projects/types",
        customColor: theme.palette.text.primary,
      },
    ],
  };

  const query = {
    page: projectTypeQueryParams.page,
    "page-size": projectTypeQueryParams.pageSize,
    ordering: projectTypeQueryParams.ordering,
    search: projectTypeQueryParams.search,
  };

  const { data: projectTypes, isLoading: projectTypesLoading } = useQuery({
    queryKey: ["projecttypes", query],
    queryFn: async () => {
      const response = await projectTypeResource.list(query);
      return {
        rows: response.data.results,
        count: response.data.count,
      };
    },
    placeholderData: keepPreviousData,
  });
  const openProjectCreateModel = () => {
    setIsCreateEditModelOpen(true);
  };
  const handleCloseModel = () => {
    setIsCreateEditModelOpen(false);
    setSelectedProjectType(null);
    formik.resetForm();
  };
  const rows = projectTypes?.rows;
  const columns = [{ field: "name", headerName: "Type", sortable: true, filterable: true }];

  // Edit
  const handleRowEdit = (projectType: ProjectType) => {
    setSelectedProjectType(projectType);
    setIsCreateEditModelOpen(true);
  };
  // Edit

  // Delete
  const handleRowDelete = (projectType: ProjectType) => {
    setSelectedProjectType(projectType);
    setIsDeletePopupOpen(true);
  };
  const deleteProjectTypeMutation = useMutation({
    mutationFn: async (id: string) => {
      return await projectResource.destroy(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projecttypes"] });
      handleDeletePopupClose();
    },
    onError: () => {
      ToastMessage.error("Failed to delete project type.");
    },
  });
  const handleDeletePopupClose = () => {
    setSelectedProjectType(null);
    setIsDeletePopupOpen(false);
  };

  //Delete

  //Project Sort
  const handleSortTable = (model: GridSortModel) => {
    const sortField = model[0]?.field || "";
    const sortDirection = model[0]?.sort || "";

    if (sortField && sortDirection) {
      setProjectTypeQueryParams(prev => ({
        ...prev,
        ordering: `${sortDirection === "desc" ? "-" : ""}${sortField}`,
      }));
    }
    return model;
  };

  //Project Filter
  const handleFilterTable = (model: GridFilterModel) => {
    return model;
  };

  //Project Pagination
  const handlePaginationChange = (model: GridPaginationModel) => {
    setProjectTypeQueryParams(prev => ({
      ...prev,
      page: model.page + 1,
      pageSize: model.pageSize,
    }));
  };

  //Project Type Create/Update
  const validationSchema = yup.object().shape({
    name: yup.string().required("Name is required"),
  });
  const { mutate: createProjectTypeMutation, isPending: createProjectTypeLoading } = useMutation({
    mutationKey: ["createProjectType"],
    mutationFn: async ({ name }: ProjectTypeFormData) => {
      return await projectResource.store({ name });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projecttypes"] });
      ToastMessage.success("Project type created successfully.");

      setIsCreateEditModelOpen(false);
      setSelectedProjectType(null);
    },
    onError: () => {
      ToastMessage.error("Project Name cannot be empty.");
    },
  });

  const { mutate: updateProjectTypeMutation, isPending: updateProjectTypeLoading } = useMutation({
    mutationKey: ["updateProjectType"],
    mutationFn: async ({ id, name }: ProjectTypeFormData) => {
      return await projectResource.update(id!, { name });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projecttypes"] });
      ToastMessage.success("Project type updated successfully.");
      setIsCreateEditModelOpen(false);
      setSelectedProjectType(null);
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  const formik = useFormik({
    initialValues: {
      name: selectedProjectType?.name ? selectedProjectType.name : "",
    },
    enableReinitialize: true,
    validationSchema,
    onSubmit: values => {
      if (selectedProjectType?.id) {
        updateProjectTypeMutation({
          id: selectedProjectType?.id,
          name: values.name,
        });
      } else {
        createProjectTypeMutation({
          id: "",
          name: values.name,
        });
      }
    },
  });

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 6,
        p: 3,
      }}
    >
      <Box>
        <ActiveBreadcrumb {...trackerPageBreadCrumbs}></ActiveBreadcrumb>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h1">Project Types</Typography>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Button variant="contained" onClick={() => openProjectCreateModel()} startIcon={<AddOutlinedIcon />}>
              Create Project Type
            </Button>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          p: 0,
          mb: 0,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
          flexWrap: "nowrap",
        }}
      >
        <Paper
          component="form"
          sx={{
            p: "2px 4px",
            display: "flex",
            alignItems: "center",
            flex: 1,
            height: 40,
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search..."
            inputProps={{ "aria-label": "search" }}
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
          <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
            <SearchIcon />
          </IconButton>
        </Paper>
      </Box>

      <Box>
        <TableComponent
          contentAlign="left"
          columns={columns}
          rows={rows}
          rowCount={projectTypes?.count}
          loading={projectTypesLoading}
          hideFooter={false}
          paginationMode={GridFeatureMode.CLIENT}
          sortingMode={GridFeatureMode.CLIENT}
          actions={[
            {
              label: "Edit",
              handler: projectType => {
                handleRowEdit(projectType as ProjectType);
              },
            },
            {
              label: "Delete",
              handler: projectType => {
                handleRowDelete(projectType as ProjectType);
              },
            },
          ]}
          onSortChange={model => {
            handleSortTable(model);
          }}
          onFilterChange={model => {
            handleFilterTable(model);
          }}
          onPaginationChange={model => {
            handlePaginationChange(model);
          }}
        />
      </Box>
      <Box></Box>
      <Dialog
        open={isDeletePopupOpen}
        onClose={handleDeletePopupClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Confirm Project Type Deletion"}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description" sx={{ color: "grey.900" }}>
            Are you sure you want to delete the project type
            <strong> {selectedProjectType?.name}</strong>? This action is irreversible and will permanently remove all
            associated data.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handleDeletePopupClose()}>Cancel</Button>
          <Button
            onClick={() => {
              if (selectedProjectType?.id) {
                deleteProjectTypeMutation.mutate(selectedProjectType.id);
              } else {
                ToastMessage.error("No project type selected.");
              }
            }}
            disabled={deleteProjectTypeMutation.isPending}
            loading={deleteProjectTypeMutation.isPending}
            loadingPosition="end"
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={isCreateEditModelOpen}
        onClose={() => {
          setIsCreateEditModelOpen(false);
          setSelectedProjectType(null);
        }}
      >
        <DialogTitle>{selectedProjectType ? "Edit Project Type" : "Create Project Type"}</DialogTitle>
        <DialogContent>
          <ProjectTypeForm
            selectedProjectType={selectedProjectType}
            onClose={handleCloseModel}
            onSubmit={data => {
              if (data.id) {
                updateProjectTypeMutation(data);
              } else {
                createProjectTypeMutation({ id: "", name: data.name });
              }
            }}
            isSubmitting={createProjectTypeLoading || updateProjectTypeLoading}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ProjectTypeScreen;
