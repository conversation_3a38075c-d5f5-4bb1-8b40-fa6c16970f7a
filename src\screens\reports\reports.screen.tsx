import React, { useState } from "react";
import { Box, Tabs, Tab, Typography, Paper, SelectChangeEvent } from "@mui/material";
import { Download as DownloadIcon } from "@mui/icons-material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { useExportTimelogs } from "@/hooks/tracker";
import { format, startOfWeek, endOfWeek, isValid, isAfter } from "date-fns";
import WeeklySummary from "./components/WeeklySummary";
import DetailedTrackerReport from "./components/DetailedTrackerReport";
import ReportFilters from "./components/ReportFilters";
import GenericActionButtons from "@/components/form-control/generic-action-buttons.component";

function ReportsScreen() {
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState({ start: startOfWeek(new Date()), end: endOfWeek(new Date()) });
  const [billableFilter, setBillableFilter] = useState(false);
  const [otFilter, setOtFilter] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [isDateValid, setIsDateValid] = useState(true);

  const exportMutation = useExportTimelogs();

  // Helper function to safely format dates
  const safeFormatDate = (date: Date) => {
    if (!date || !isValid(date)) {
      return format(new Date(), "yyyy-MM-dd");
    }
    return format(date, "yyyy-MM-dd");
  };

  // Only make API call when dates are valid by checking required parameters
  const shouldEnableQuery =
    isDateValid && isValid(dateRange.start) && isValid(dateRange.end) && !isAfter(dateRange.start, dateRange.end);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleProjectsChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedProjects(typeof value === "string" ? value.split(",") : value);
  };

  const handleUsersChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedUsers(typeof value === "string" ? value.split(",") : value);
  };

  const handleExport = () => {
    // Prevent export if dates are invalid
    if (!isDateValid) {
      return;
    }

    exportMutation.mutate({
      user__in: selectedUsers.length > 0 ? selectedUsers : undefined,
      start_time__gte: safeFormatDate(dateRange.start),
      end_time__lte: safeFormatDate(dateRange.end),
      project__in: selectedProjects.length > 0 ? selectedProjects : undefined,
      is_billable: billableFilter || undefined,
      is_ot: otFilter || undefined,
    });
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom mb={4}>
          Reports
        </Typography>
        <ReportFilters
          dateRange={dateRange}
          setDateRange={setDateRange}
          selectedProjects={selectedProjects}
          handleProjectsChange={handleProjectsChange}
          selectedUsers={selectedUsers}
          handleUsersChange={handleUsersChange}
          billableFilter={billableFilter}
          setBillableFilter={setBillableFilter}
          otFilter={otFilter}
          setOtFilter={setOtFilter}
          onValidationChange={setIsDateValid}
        />
        <GenericActionButtons
          buttons={[
            {
              label: exportMutation.isPending ? "Exporting..." : "Export CSV",
              onClick: handleExport,
              variant: "contained",
              color: "primary",
              startIcon: <DownloadIcon />,
              disabled: exportMutation.isPending || !isDateValid,
            },
          ]}
        />
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="report tabs">
          <Tab label="Weekly Summary" />
          <Tab label="Detailed Tracker Report" />
        </Tabs>
        <Paper sx={{ mt: 2, p: 2 }}>
          {!isDateValid && (
            <Typography variant="body1" color="error" textAlign="center" sx={{ py: 4 }}>
              Please fix the date validation errors before viewing reports.
            </Typography>
          )}
          {isDateValid && tabValue === 0 && (
            <WeeklySummary
              dateRange={dateRange}
              selectedProjects={selectedProjects}
              selectedUsers={selectedUsers}
              billableFilter={billableFilter}
              otFilter={otFilter}
              enabled={shouldEnableQuery}
            />
          )}
          {isDateValid && tabValue === 1 && (
            <DetailedTrackerReport
              dateRange={dateRange}
              selectedProjects={selectedProjects}
              selectedUsers={selectedUsers}
              billableFilter={billableFilter}
              otFilter={otFilter}
              enabled={shouldEnableQuery}
            />
          )}
        </Paper>
      </Box>
    </LocalizationProvider>
  );
}

export default ReportsScreen;
