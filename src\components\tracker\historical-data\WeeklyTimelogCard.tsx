import React from "react";
import { Box, Typography, Paper, Stack, Divider } from "@mui/material";
import { format, parseISO } from "date-fns";
import { formatTime } from "@/utils/time";
import DailyTimelogGroup from "./DailyTimelogGroup";
import { TimelogData, WeeklyTimelogCardProps } from "@/types/tracker";

const WeeklyTimelogCard: React.FC<WeeklyTimelogCardProps> = ({ weeklyData, onRestartTimer, isTracking }) => {
  const totalDuration = React.useMemo(() => {
    return weeklyData.logs.reduce((total, log) => {
      if (!log.end_time) return total;
      try {
        const start = new Date(log.start_time);
        const end = new Date(log.end_time);
        const diffInSeconds = Math.floor((end.getTime() - start.getTime()) / 1000);
        return total + diffInSeconds;
      } catch {
        return total;
      }
    }, 0);
  }, [weeklyData.logs]);

  const formattedTotalTime = React.useMemo(() => {
    if (totalDuration <= 0) return "0h 0m";
    return formatTime(totalDuration);
  }, [totalDuration]);

  const weekDateRange = React.useMemo(() => {
    try {
      const start = parseISO(weeklyData.week);
      const end = new Date(start.getTime() + 6 * 24 * 60 * 60 * 1000);
      return `${format(start, "MMM d")} - ${format(end, "MMM d, yyyy")}`;
    } catch {
      return `Week of ${weeklyData.week}`;
    }
  }, [weeklyData.week]);

  const dailyLogs = React.useMemo(() => {
    const logsByDay: Record<string, TimelogData[]> = {};

    weeklyData.logs.forEach(log => {
      try {
        const logDate = parseISO(log.start_time);
        const dateKey = format(logDate, "yyyy-MM-dd");

        if (!logsByDay[dateKey]) {
          logsByDay[dateKey] = [];
        }
        logsByDay[dateKey].push(log);
      } catch {
        // Silently handle log processing errors
      }
    });

    return logsByDay;
  }, [weeklyData.logs]);

  const sortedDateKeys = Object.keys(dailyLogs).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <Typography variant="h6">{weekDateRange}</Typography>

        <Stack direction="row" spacing={1} alignItems="center">
          <Typography variant="body2">Total: {formattedTotalTime}</Typography>
        </Stack>
      </Box>

      <Divider sx={{ my: 1 }} />

      <Box sx={{ maxHeight: 400, overflowY: "auto" }}>
        {sortedDateKeys.length > 0 ? (
          sortedDateKeys.map(dateKey => (
            <React.Fragment key={dateKey}>
              <DailyTimelogGroup
                date={dateKey}
                logs={dailyLogs[dateKey] || []}
                onRestartTimer={onRestartTimer}
                isTracking={isTracking}
              />
              <Divider sx={{ my: 1 }} />
            </React.Fragment>
          ))
        ) : (
          <Box p={2} textAlign="center">
            <Typography variant="body2" color="textSecondary">
              No time entries for this week
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default WeeklyTimelogCard;
