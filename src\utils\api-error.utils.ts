// API Error Response Type
export interface ApiErrorResponse {
  response?: {
    data?: {
      error?: string;
      message?: string;
    };
  };
  message?: string;
}

// Helper function to extract error message from API error response
export const getErrorMessage = (error: unknown, fallbackMessage: string): string => {
  const err = error as ApiErrorResponse;
  return err?.response?.data?.error || err?.response?.data?.message || err?.message || fallbackMessage;
};
