import React, { useEffect, useState } from "react";
import { useDebounce } from "@/hooks/tracker/useDebounce";
import {
  Button,
  Checkbox,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  TextField,
  Box,
  Typography,
  FormHelperText,
  Autocomplete,
} from "@mui/material";
import GenericFormControl from "@/components/form-control/generic-form.component";
import { useFormikContext } from "formik";
import { Group, IUserForm } from "@/interfaces/teams.interface";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import Resource from "@/core/api/resource";
import { ITeamMemberFormProps } from "@/interface/team";
import { Country } from "@/types/teams";

const TeamMemberForm: React.FC<ITeamMemberFormProps> = props => {
  const userResource = new Resource("auth/groups");
  const countryResource = new Resource("users/country");
  const timezoneResource = new Resource("timezones");
  const { values, setFieldValue, errors, touched } = useFormikContext<IUserForm>();

  const [timezoneSearch, setTimezoneSearch] = useState("");
  const { debouncedValue: debouncedTimezoneSearch, setDebouncedValue: setDebouncedTimezoneSearch } = useDebounce(300);

  const { data: allGroupsList } = useQuery({
    queryKey: ["auth/groups"],
    queryFn: async () => {
      const response = await userResource.list();
      return {
        rows: response.data.results,
      };
    },
    placeholderData: keepPreviousData,
  });

  const { data: countriesList } = useQuery({
    queryKey: ["users/country"],
    queryFn: async () => {
      const response = await countryResource.list();
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const { data: timezonesList } = useQuery({
    queryKey: ["timezones"],
    queryFn: async () => {
      const response = await timezoneResource.list();
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  // Filter timezones based on search
  const filteredTimezones = (timezonesList || []).filter((timezone: string) => {
    return (
      timezone && typeof timezone === "string" && timezone.toLowerCase().includes(debouncedTimezoneSearch.toLowerCase())
    );
  });

  const [groups, setGroups] = React.useState<string[]>([]);

  const handleChange = (event: SelectChangeEvent<string[]>) => {
    const {
      target: { value },
    } = event;
    const selectedIds = typeof value === "string" ? value.split(",") : value;
    setGroups(selectedIds);

    if (allGroupsList?.rows) {
      const selectedGroups = allGroupsList.rows.filter((group: Group) => selectedIds.includes(String(group.id)));
      setFieldValue("groups", selectedGroups);
    }
  };
  const handleTimezoneChange = (event: SelectChangeEvent<string>) => {
    const timezoneId = event.target.value;
    setFieldValue("timezone", timezoneId);
  };

  useEffect(() => {
    if (values?.groups) {
      const groupIds = values.groups.map((group: Group) => String(group.id));
      setGroups(groupIds);
    }
  }, [values?.groups]);

  // Don't render until essential data is loaded
  if (!allGroupsList?.rows || !timezonesList) {
    return <div>Loading form data...</div>;
  }

  return (
    <>
      <GenericFormControl
        id="firstName"
        label="First name"
        type="text"
        value={values?.first_name}
        onChangeFn={e => setFieldValue("first_name", e.target.value)}
        error={touched.first_name && Boolean(errors.first_name)}
        helperText={touched.first_name && errors.first_name ? errors.first_name : undefined}
      />
      <GenericFormControl
        id="middlename"
        label="Middle name"
        type="text"
        value={values?.middle_name}
        onChangeFn={e => setFieldValue("middle_name", e.target.value)}
        error={touched.middle_name && Boolean(errors.middle_name)}
        helperText={touched.middle_name && errors.middle_name ? errors.middle_name : undefined}
      />
      <GenericFormControl
        id="lastName"
        label="Last name"
        type="text"
        value={values?.last_name}
        onChangeFn={e => setFieldValue("last_name", e.target.value)}
        error={touched.last_name && Boolean(errors.last_name)}
        helperText={touched.last_name && errors.last_name ? errors.last_name : undefined}
      />
      <GenericFormControl
        id="phone"
        label="Phone"
        type="text"
        value={values?.phone}
        onChangeFn={e => setFieldValue("phone", e.target.value)}
        error={touched.phone && Boolean(errors.phone)}
      />

      <Autocomplete
        id="country-autocomplete"
        options={countriesList}
        getOptionLabel={option => option.display_name}
        value={countriesList?.find((c: Country) => c.name === values.country) || null}
        onChange={(_event, newValue) => {
          setFieldValue("country", newValue ? newValue.name : "");
        }}
        renderInput={params => (
          <TextField
            {...params}
            label="Country"
            error={Boolean(touched.country && errors.country)}
            margin="normal"
            fullWidth
          />
        )}
      />

      <FormControl fullWidth margin="normal" error={Boolean(errors.timezone && touched.timezone)}>
        <InputLabel id="timezone-select-label">Timezone</InputLabel>
        <Select
          labelId="timezone-select-label"
          id="timezone"
          value={(() => {
            const timezoneValue = values?.timezone || "";
            return timezoneValue;
          })()}
          label="Timezone"
          onChange={handleTimezoneChange}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
              },
            },
          }}
        >
          <Box sx={{ p: 1, position: "sticky", top: 0, backgroundColor: "white", zIndex: 1 }}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search timezones..."
              value={timezoneSearch}
              onChange={e => {
                setTimezoneSearch(e.target.value);
                setDebouncedTimezoneSearch(e.target.value);
              }}
              onClick={e => e.stopPropagation()}
              onKeyDown={e => e.stopPropagation()}
            />
          </Box>
          {filteredTimezones.length === 0 && timezoneSearch && (
            <MenuItem disabled>
              <Typography variant="body2" color="textSecondary">
                No timezones found matching "{timezoneSearch}"
              </Typography>
            </MenuItem>
          )}
          {filteredTimezones.map((timezone: string) => {
            return (
              <MenuItem key={timezone} value={timezone}>
                {timezone}
              </MenuItem>
            );
          })}
        </Select>
        {Boolean(errors.timezone && touched.timezone) && (
          <FormHelperText>
            {typeof errors.timezone === "string" ? errors.timezone : "Please select a timezone"}
          </FormHelperText>
        )}
      </FormControl>
      <GenericFormControl
        id="email"
        label="Email"
        type="email"
        value={values?.email}
        onChangeFn={e => setFieldValue("email", e.target.value)}
        error={touched.email && Boolean(errors.email)}
        helperText={touched.email && errors.email ? errors.email : undefined}
      />

      <FormControl fullWidth margin="normal" error={Boolean(errors.groups && touched.groups)}>
        <InputLabel id="groups-select-label">Groups</InputLabel>
        <Select
          labelId="groups-select-label"
          id="groups"
          multiple
          value={groups}
          onChange={handleChange}
          input={<OutlinedInput label="Select Role" sx={{ color: "black" }} />}
          renderValue={selected => {
            if (!allGroupsList?.rows) {
              return "";
            }
            return allGroupsList.rows
              .filter((group: Group) => group && group.id !== undefined && selected.includes(String(group.id)))
              .map((group: Group) => group.name)
              .join(", ");
          }}
          sx={{
            "& .MuiSelect-select": {
              color: "black",
            },
            "&. MuiCheckbox-root": {
              fill: "black",
            },
          }}
        >
          {allGroupsList?.rows?.map((group: Group) => {
            if (!group || group.id === undefined) {
              return null;
            }
            return (
              <MenuItem key={group.id} value={String(group.id)}>
                <Checkbox checked={groups.includes(String(group.id))} />
                <ListItemText primary={group.name_display} />
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>

      <Button
        variant="contained"
        type="submit"
        sx={{
          mt: 2,
          px: 2,
          py: 1.5,
          width: "100%",
          fontWeight: 600,
          fontSize: "1rem",
          textTransform: "none",
          borderRadius: 2,
          boxShadow: "0px 3px 6px rgba(0, 0, 0, 0.1)",
          backgroundColor: "custom.brand.primary",
          color: "white",
          "&:hover": {
            backgroundColor: "custom.brand.primary",
            filter: "brightness(0.95)",
          },
        }}
      >
        {props?.selectedData?.id ? "Update" : " Save "}
      </Button>
    </>
  );
};

export default TeamMemberForm;
