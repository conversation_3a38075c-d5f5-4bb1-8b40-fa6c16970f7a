import React, { useMemo, useState } from "react";
import { Autocomplete, debounce, TextField } from "@mui/material";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import Resource from "@/core/api/resource";
import { ProjectOption, ProjectAutocompleteProps, ProjectResponse } from "@/types/tracker";

const ProjectAutocomplete: React.FC<ProjectAutocompleteProps> = ({
  userId,
  value,
  onChange,
  label = "Projects",
  error,
  helperText,
}) => {
  const [searchTerm, setSearchTerm] = useState("");

  const projectResource = useMemo(() => new Resource("projects/user"), []);

  const { data: projectList } = useQuery({
    queryKey: ["projectList", userId, searchTerm],
    queryFn: () => projectResource.list({ user_id: userId, search: searchTerm }),
    enabled: !!userId,
    placeholderData: keepPreviousData,
  });
  const projectOptions: ProjectOption[] =
    projectList?.data?.results.map((project: ProjectResponse) => ({
      label: project.project.name,
      value: project.project.id,
    })) || [];

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchTerm(value);
      }, 300),
    []
  );

  return (
    <Autocomplete
      options={projectOptions}
      getOptionLabel={option => option.label}
      onInputChange={(_event, newInputValue) => {
        debouncedSearch(newInputValue);
      }}
      onChange={(_event, newValue) => {
        onChange(newValue?.value || "");
      }}
      value={projectOptions.find(option => option.value === value) || null}
      renderInput={params => <TextField {...params} label={label} error={error} helperText={helperText} />}
      sx={{ width: 300 }}
    />
  );
};

export default ProjectAutocomplete;
