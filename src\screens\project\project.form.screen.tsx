import React from "react";
import Grid from "@mui/material/Grid";
import {
  Box,
  Button,
  TextField,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { Client, Project, ProjectFormData, ProjectType, statusLabels } from "@/interfaces/project.interface";
import theme from "@/theme/theme";
import { HEX_COLOR_REGEX } from "@/utils/regex";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import Resource from "@/core/api/resource";

interface ProjectFormProps {
  selectedProject: Project | null;
  onSubmit: (data: ProjectFormData) => void;
  isSubmitting: boolean;
  onClose: () => void;
}

const validationSchema = yup.object().shape({
  name: yup.string().required("Name is required"),
  color_hex_value: yup.string().matches(HEX_COLOR_REGEX, "Invalid hex color (e.g. #000, #ffffff)"),
  is_public: yup.boolean(),
  is_billable: yup.boolean(),
  clickup_space_id: yup.string().nullable(),
  status: yup.string().required("Status is required"),
  project_type: yup.string().required("Project type is required"),
});

const ProjectForm: React.FC<ProjectFormProps> = ({ selectedProject, onSubmit, isSubmitting }) => {
  const clientResource = new Resource("clients");
  const projectTypeResource = new Resource("projects/types");
  const {
    //allClients
    data: allClients,
  } = useQuery<Client[]>({
    queryKey: ["allClients"],
    queryFn: async () => {
      const response = await clientResource.list({ "page-size": 100 });
      return response.data.results;
    },
  });
  const {
    //projectTypes
    data: projectTypes,
  } = useQuery<ProjectType[]>({
    queryKey: ["projecttypes"],
    queryFn: async () => {
      const response = await projectTypeResource.list({ "page-size": 100 });
      return response.data.results;
    },
    placeholderData: keepPreviousData,
  });

  //Project Form
  const formik = useFormik({
    initialValues: {
      name: selectedProject?.name || "",
      color_hex_value: selectedProject?.color_hex_value
        ? `${selectedProject?.color_hex_value}`
        : theme.palette.primary.main,
      is_public: selectedProject?.is_public ?? false,
      is_billable: selectedProject?.is_billable ?? false,
      clickup_space_id: selectedProject?.clickup_space_id || "",
      project_type: selectedProject?.project_type?.id || "",
      status: selectedProject?.status || "",
      client: selectedProject?.client || "",
    },
    enableReinitialize: true,
    validationSchema,
    onSubmit: values => {
      onSubmit(values);
    },
  });

  return (
    <Box sx={{ mt: 1 }}>
      <form onSubmit={formik.handleSubmit} noValidate>
        <Grid container spacing={2}>
          <Grid size={6}>
            <TextField
              fullWidth
              name="name"
              value={formik.values.name}
              variant="outlined"
              onChange={formik.handleChange}
              error={formik.touched.name && !!formik.errors.name}
              helperText={formik.touched.name && formik.errors.name}
              label="Project Name"
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            ></TextField>
          </Grid>
          <Grid size={6}>
            <FormControl fullWidth error={formik.touched.client && !!formik.errors.client}>
              <Select
                name="client"
                onChange={formik.handleChange}
                value={formik.values.client}
                displayEmpty
                renderValue={selected => {
                  if (!selected) {
                    return (
                      <span
                        style={{
                          color: theme.palette.custom.brand.silverChalice,
                        }}
                      >
                        Select Client
                      </span>
                    );
                  }
                  const matchedClient = allClients?.find(client => client.id === selected);
                  return matchedClient?.name || "Unknown";
                }}
              >
                <MenuItem value="" disabled>
                  Select Client
                </MenuItem>
                {allClients?.map(client => (
                  <MenuItem key={client.id} value={client.id}>
                    {client.name}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.status && formik.errors.status && <FormHelperText>{formik.errors.status}</FormHelperText>}
            </FormControl>
          </Grid>
          <Grid size={6}>
            <FormControl fullWidth error={formik.touched.status && !!formik.errors.status}>
              <Select
                name="status"
                onChange={formik.handleChange}
                value={formik.values.status}
                displayEmpty
                renderValue={selected => {
                  if (!selected) {
                    return (
                      <span
                        style={{
                          color: theme.palette.custom.brand.silverChalice,
                        }}
                      >
                        Select Status
                      </span>
                    );
                  }
                  return statusLabels[selected as keyof typeof statusLabels] || "Unknown";
                }}
              >
                <MenuItem value="" disabled>
                  Select status
                </MenuItem>
                {Object.entries(statusLabels).map(([value, label]) => (
                  <MenuItem key={value} value={value}>
                    {label}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.status && formik.errors.status && <FormHelperText>{formik.errors.status}</FormHelperText>}
            </FormControl>
          </Grid>
          <Grid size={6}>
            <TextField
              fullWidth
              name="clickup_space_id"
              value={formik.values.clickup_space_id}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              variant="outlined"
              label="Clickup Id"
              error={formik.touched.clickup_space_id && !!formik.errors.clickup_space_id}
              helperText={formik.touched.clickup_space_id && formik.errors.clickup_space_id}
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            />
          </Grid>

          <Grid size={6}>
            <FormControl fullWidth error={formik.touched.project_type && !!formik.errors.project_type}>
              <Select
                name="project_type"
                value={formik.values.project_type}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                displayEmpty
                renderValue={selected => {
                  if (!selected) {
                    return (
                      <span
                        style={{
                          color: theme.palette.custom.brand.silverChalice,
                        }}
                      >
                        Select Project Type
                      </span>
                    );
                  }
                  const matchedType = projectTypes?.find(type => type.id === selected);
                  return matchedType?.name || "Unknown";
                }}
              >
                <MenuItem value="" disabled>
                  Select Project Type
                </MenuItem>
                {projectTypes?.map(type => (
                  <MenuItem key={type.id} value={type.id}>
                    {type.name}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.project_type && formik.errors.project_type && (
                <FormHelperText>{formik.errors.project_type}</FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid size={12}>
            <FormControlLabel
              control={
                <Checkbox
                  name="is_public"
                  checked={formik.values.is_public}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  sx={{
                    color: "grey.900",
                    "&.Mui-checked": {
                      color: "grey.900",
                    },
                  }}
                />
              }
              label="Is Public"
              sx={{ color: "grey.900" }}
            />
            <FormControlLabel
              control={
                <Checkbox
                  name="is_billable"
                  checked={formik.values.is_billable}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  sx={{
                    color: "grey.900",
                    "&.Mui-checked": {
                      color: "grey.900",
                    },
                  }}
                />
              }
              label="Billable"
              sx={{ color: "grey.900" }}
            />
          </Grid>

          <Grid size={12}>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
              loading={isSubmitting}
              loadingPosition="end"
            >
              {isSubmitting ? (selectedProject ? "Updating..." : "Creating...") : selectedProject ? "Update" : "Create"}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default ProjectForm;
