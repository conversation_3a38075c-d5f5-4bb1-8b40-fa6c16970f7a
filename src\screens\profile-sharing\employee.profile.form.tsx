import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  <PERSON>ton,
  Grid,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  AlertTitle,
  Typography,
} from "@mui/material";
import { Formik, Form, FieldArray, FormikHelpers } from "formik";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import { EmployeeProfileFormProps, ValidationItem } from "@/interfaces/employeeProfile.interface";
import {
  FormValues,
  createInitialFormValues,
  mapProfileToFormValues,
  convertFormValuesToBackendFormat,
} from "@/utils/employee-profile-form-utils";
import {
  employeeProfileValidationSchema,
  validateWorkExperienceSection,
  validateEducationSection,
  validateLanguagesSection,
  validateBackgroundSection,
  validateAvailabilitySection,
  validateNoDuplicates,
  validateWorkExperienceOverlap,
} from "@/utils/employee-profile-validation";
import WorkExperienceSection from "@/components/profiles/work-experience-section.component";
import BackgroundSection from "@/components/profiles/background-section.component";
import LanguagesSection from "@/components/profiles/languages-section.component";
import EducationSection from "@/components/profiles/education-section.component";
import AvailabilitySection from "@/components/profiles/availability-section.component";
import ToastMessage from "@/components/toast/toast.component";

const EmployeeProfileForm: React.FC<EmployeeProfileFormProps> = ({
  open = true,
  onClose,
  onSave,
  employeeProfile,
  selectedEmployeeId,
  modal = true,
}) => {
  const [validationErrors, setValidationErrors] = React.useState<string[]>([]);
  const [isValidating, setIsValidating] = React.useState(false);

  const initialValues = React.useMemo(() => {
    if (employeeProfile) return mapProfileToFormValues(employeeProfile);
    const initial = createInitialFormValues();
    initial.user_id = selectedEmployeeId || "";
    return initial;
  }, [employeeProfile, selectedEmployeeId]);

  const runComprehensiveValidation = (values: FormValues): string[] => {
    const allErrors: string[] = [];

    if (!values.user_id) allErrors.push("Employee selection is required");

    values.details.forEach(section => {
      if (!section.children || section.children.length === 0) {
        allErrors.push(`${section.value} section cannot be empty`);
        return;
      }

      switch (section.value) {
        case "Work Experience":
          allErrors.push(...validateWorkExperienceSection(section.children));
          break;
        case "Education":
          allErrors.push(...validateEducationSection(section.children));
          break;
        case "Languages":
          allErrors.push(...validateLanguagesSection(section.children));
          break;
        case "Background":
          allErrors.push(...validateBackgroundSection(section.children));
          break;
        case "Availability":
          allErrors.push(...validateAvailabilitySection(section.children));
          break;
      }
    });

    allErrors.push(...validateNoDuplicates(values));
    allErrors.push(...validateWorkExperienceOverlap(values));
    return allErrors.filter(Boolean);
  };

  const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
    try {
      setIsValidating(true);
      setValidationErrors([]);
      const errors = runComprehensiveValidation(values);
      if (errors.length > 0) {
        setValidationErrors(errors);
        ToastMessage.error(`Found ${errors.length} error(s). Fix before submitting.`);
        return;
      }

      const payload = convertFormValuesToBackendFormat(values);
      await onSave(payload);
      ToastMessage.success("Profile saved successfully!");
      setValidationErrors([]);
      onClose?.();
    } catch {
      ToastMessage.error("Failed to save profile.");
    } finally {
      setSubmitting(false);
      setIsValidating(false);
    }
  };

  const getSectionValidationStatus = (sectionName: string, sectionData: { children?: ValidationItem[] }) => {
    if (!sectionData?.children || sectionData.children.length === 0) {
      return { hasItems: false, hasErrors: true, errorCount: 1 };
    }

    let sectionErrors: string[] = [];
    switch (sectionName) {
      case "Work Experience":
        sectionErrors = validateWorkExperienceSection(sectionData.children);
        break;
      case "Education":
        sectionErrors = validateEducationSection(sectionData.children);
        break;
      case "Languages":
        sectionErrors = validateLanguagesSection(sectionData.children);
        break;
      case "Background":
        sectionErrors = validateBackgroundSection(sectionData.children);
        break;
      case "Availability":
        sectionErrors = validateAvailabilitySection(sectionData.children);
        break;
    }

    return {
      hasItems: sectionData.children.length > 0,
      hasErrors: sectionErrors.length > 0,
      errorCount: sectionErrors.length,
    };
  };

  const formBody = (
    <Formik
      key={employeeProfile?.id || selectedEmployeeId || "new"}
      initialValues={initialValues}
      enableReinitialize
      validationSchema={employeeProfileValidationSchema}
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue, isSubmitting }) => (
        <Form>
          <Box sx={{ p: modal ? 0 : 1, mb: 2 }}>
            {validationErrors.length > 0 && (
              <Alert severity="error" sx={{ mb: 3 }}>
                <AlertTitle>
                  {validationErrors.length} validation error
                  {validationErrors.length > 1 ? "s" : ""}
                </AlertTitle>
                <ul style={{ marginLeft: 16 }}>
                  {validationErrors.map((err, i) => (
                    <li key={i}>{err}</li>
                  ))}
                </ul>
              </Alert>
            )}

            <Grid container spacing={3}>
              <Grid size={12}>
                <FieldArray name="details">
                  {() =>
                    values.details.map((section, sectionIndex) => {
                      const status = getSectionValidationStatus(section.value, section);
                      return (
                        <Accordion key={sectionIndex} defaultExpanded>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                width: "100%",
                              }}
                            >
                              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                                {section.value}
                              </Typography>
                              {status.hasItems && (
                                <Box
                                  sx={{
                                    px: 1.5,
                                    py: 0.5,
                                    borderRadius: 1,
                                    bgcolor: "success.light",
                                    color: "success.contrastText",
                                    fontSize: "0.75rem",
                                    fontWeight: "medium",
                                  }}
                                >
                                  {section.children?.length} item
                                </Box>
                              )}
                              {status.hasErrors && (
                                <Box
                                  sx={{
                                    px: 1.5,
                                    py: 0.5,
                                    borderRadius: 1,
                                    bgcolor: "error.light",
                                    color: "error.contrastText",
                                    fontSize: "0.75rem",
                                    fontWeight: "medium",
                                  }}
                                >
                                  {status.errorCount} error
                                </Box>
                              )}
                            </Box>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Box>
                              {section.value === "Background" && (
                                <BackgroundSection
                                  sectionIndex={sectionIndex}
                                  section={section}
                                  setFieldValue={setFieldValue}
                                />
                              )}
                              {section.value === "Languages" && (
                                <LanguagesSection
                                  sectionIndex={sectionIndex}
                                  section={section}
                                  setFieldValue={setFieldValue}
                                />
                              )}
                              {section.value === "Education" && (
                                <EducationSection
                                  sectionIndex={sectionIndex}
                                  section={section}
                                  setFieldValue={setFieldValue}
                                />
                              )}
                              {section.value === "Work Experience" && (
                                <WorkExperienceSection
                                  sectionIndex={sectionIndex}
                                  section={section}
                                  setFieldValue={setFieldValue}
                                />
                              )}
                              {section.value === "Availability" && (
                                <AvailabilitySection
                                  sectionIndex={sectionIndex}
                                  section={section}
                                  setFieldValue={setFieldValue}
                                />
                              )}
                            </Box>
                          </AccordionDetails>
                        </Accordion>
                      );
                    })
                  }
                </FieldArray>
              </Grid>
            </Grid>
          </Box>

          {modal ? (
            <DialogActions>
              <Button onClick={onClose} disabled={isSubmitting || isValidating}>
                Cancel
              </Button>
              <Button type="submit" variant="contained" disabled={isSubmitting || isValidating}>
                {isSubmitting || isValidating ? "Saving..." : "Save Profile"}
              </Button>
            </DialogActions>
          ) : (
            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 2 }}>
              <Button onClick={onClose} disabled={isSubmitting || isValidating}>
                Cancel
              </Button>
              <Button type="submit" variant="contained" disabled={isSubmitting || isValidating}>
                {isSubmitting || isValidating ? "Saving..." : "Save Profile"}
              </Button>
            </Box>
          )}
        </Form>
      )}
    </Formik>
  );

  if (modal) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Typography variant="h5">{employeeProfile ? "Edit Employee Profile" : "Create Employee Profile"}</Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            Fill out all sections carefully.
          </Typography>
        </DialogTitle>
        <DialogContent>{formBody}</DialogContent>
      </Dialog>
    );
  }

  return <Box>{formBody}</Box>;
};

export default EmployeeProfileForm;
