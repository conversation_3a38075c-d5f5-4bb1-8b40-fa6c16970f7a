import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AuthResponse, AuthState, User } from "@/types";

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  groups: [],
  tenant: null,
  stay_logged_in: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAuthUser: (state, action: PayloadAction<AuthResponse>) => {
      state.groups = action.payload.groups;
      state.tenant = action.payload.tenant;
      state.user = action.payload.user;
      state.isAuthenticated = true;
      state.stay_logged_in = action.payload.stay_logged_in ?? false;
    },
    setUserProfile: (state, action: PayloadAction<{ user: User }>) => {
      state.user = action.payload.user;
    },
    clearAuth: (state) => {
      // state = initialState;
      state.isAuthenticated = false;
      state.user = null;
      state.groups = [];
      state.tenant = null;
      state.stay_logged_in = false;
    },
  },
});

export const { setAuthUser, setUserProfile, clearAuth } = authSlice.actions;
export default authSlice.reducer;
