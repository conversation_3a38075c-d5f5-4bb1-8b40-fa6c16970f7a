import React from "react";
import { TextField, Grid, Typography, Autocomplete, Chip } from "@mui/material";
import { DetailNode } from "@/utils/employee-profile-form-utils";
import { allSkills } from "./constants";

interface BackgroundSectionProps {
  sectionIndex: number;
  section: DetailNode;
  setFieldValue: (field: string, value: unknown) => void;
}

const BackgroundSection: React.FC<BackgroundSectionProps> = ({ sectionIndex, section, setFieldValue }) => {
  const handleSkillsChange = (groupIndex: number, newSkills: string[]) => {
    const newChildren = newSkills.map(skill => ({ value: skill }));
    setFieldValue(`details[${sectionIndex}].children[${groupIndex}].children`, newChildren);
  };

  return (
    <Grid container spacing={2}>
      {section.children?.map((group, groupIndex) => (
        <Grid size={12} key={groupIndex}>
          <Typography variant="subtitle1" gutterBottom sx={{ textTransform: "capitalize" }}>
            {group.value}
          </Typography>
          <Autocomplete
            multiple
            options={allSkills}
            freeSolo
            value={group.children?.map(child => child.value) || []}
            onChange={(_, newValue) => handleSkillsChange(groupIndex, newValue)}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />
              ))
            }
            renderInput={params => <TextField {...params} placeholder={`Add ${group.value}...`} fullWidth />}
          />
        </Grid>
      ))}
    </Grid>
  );
};

export default BackgroundSection;
