import Grid from "@mui/material/Grid";
import List from "@mui/material/List";
import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import ListItemIcon from "@mui/material/ListItemIcon";
import Checkbox from "@mui/material/Checkbox";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import { useEffect, useState } from "react";
import Resource from "@/core/api/resource";
import { useMutation, useQuery, keepPreviousData } from "@tanstack/react-query";
import TextField from "@mui/material/TextField";
import { Identifiable, ManagePermissionsProps, Permission } from "@/interfaces/teams.interface";
import ToastMessage from "@/components/toast/toast.component";
const permissionResource = new Resource("auth/permissions");
const groupsResource = new Resource("auth/groups");
const assignedPermissionResource = new Resource("auth/groups");

function not<T extends Identifiable>(a: T[], b: T[]): T[] {
  return a.filter(value => !b.some(item => item.id === value.id));
}

function intersection(a: Permission[], b: Permission[]): Permission[] {
  return a.filter(value => b.some(item => item.id === value.id));
}

function union<T extends Identifiable>(a: T[], b: T[]): T[] {
  return [...a, ...not(b, a)];
}

export default function ManagePermissions({ group }: ManagePermissionsProps) {
  const [filter, setFilter] = useState<string>("");
  const [checked, setChecked] = useState<Permission[]>([]);
  const [left, setLeft] = useState<Permission[]>([]);
  const [right, setRight] = useState<Permission[]>([]);

  const { data: allPermissions = [], isLoading: isAllLoading } = useQuery({
    queryKey: ["allPermissions"],
    queryFn: async () => {
      const response = await permissionResource.list();
      return response.data || [];
    },
    placeholderData: keepPreviousData,
  });

  const {
    data: selectedPermissionsData,
    isLoading: isSelectedPermissionsLoading,
    refetch,
  } = useQuery({
    queryKey: ["assignedPermissions", group.id],
    queryFn: async () => {
      const response = await assignedPermissionResource.get(`${group.id}/permissions`);
      return response.data || [];
    },
    enabled: !!group?.id,
  });

  // Assign Permissions Mutation
  const assignPermissionMutation = useMutation({
    mutationFn: async (permissions: number[]) => {
      return groupsResource.custom(`${group.id}/permissions/add/`, {
        method: "POST",
        data: { permissions },
      });
    },
  });

  // Remove Permissions Mutation
  const removePermissionMutation = useMutation({
    mutationFn: async (permissions: number[]) => {
      return groupsResource.custom(`${group.id}/permissions/remove/`, {
        method: "POST",
        data: { permissions },
      });
    },
  });
  // Refetch permissions whenever group.id changes
  useEffect(() => {
    if (group?.id) {
      refetch();
    }
  }, [group?.id, refetch]);

  // Set left and right once both permissions are loaded
  useEffect(() => {
    if (!isAllLoading && !isSelectedPermissionsLoading) {
      setRight(selectedPermissionsData || []);
      setLeft(not(allPermissions, selectedPermissionsData || []));
    }
  }, [allPermissions, selectedPermissionsData, isAllLoading, isSelectedPermissionsLoading]);

  const leftChecked = intersection(checked, left);
  const rightChecked = intersection(checked, right);

  const handleToggle = (value: Permission) => () => {
    const currentIndex = checked.findIndex(item => item.id === value.id);
    const newChecked = [...checked];

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setChecked(newChecked);
  };

  const numberOfChecked = (items: Permission[]): number => {
    return intersection(checked, items).length;
  };

  const handleToggleAll = (items: Permission[]) => () => {
    if (numberOfChecked(items) === items.length) {
      setChecked(not(checked, items));
    } else {
      setChecked(union(checked, items));
    }
  };

  const handlePermissionsAdd = () => {
    const permissionIds: number[] = leftChecked.map((p: Permission) => p.id);

    assignPermissionMutation.mutate(permissionIds, {
      onSuccess: () => {
        setRight((prev: Permission[]) => prev.concat(leftChecked));
        setLeft(not(left, leftChecked));
        setChecked(not(checked, leftChecked));
        ToastMessage.success("Permissions added successfully");
      },
      onError: () => {
        ToastMessage.error(`Failed to add permissions`);
      },
    });
  };

  const handlePermissionsRemove = () => {
    const permissionIds: number[] = rightChecked.map((p: Permission) => p.id);

    removePermissionMutation.mutate(permissionIds, {
      onSuccess: () => {
        setLeft(prev => prev.concat(rightChecked));
        setRight(not(right, rightChecked));
        setChecked(not(checked, rightChecked));
        ToastMessage.success("Permissions removed successfully");
      },
      onError: () => {
        ToastMessage.error("Failed to remove permissions");
      },
    });
  };

  const listPermissions = (title: string, items: Permission[], filter: string) => {
    const filteredItems = items.filter(item => item.name.toLowerCase().includes(filter.toLowerCase()));

    return (
      <Card>
        <CardHeader
          sx={{ px: 2, py: 1 }}
          avatar={
            <Checkbox
              onClick={handleToggleAll(filteredItems)}
              checked={numberOfChecked(filteredItems) === filteredItems.length && filteredItems.length !== 0}
              indeterminate={
                numberOfChecked(filteredItems) !== filteredItems.length && numberOfChecked(filteredItems) !== 0
              }
              disabled={filteredItems.length === 0}
              inputProps={{ "aria-label": "all items selected" }}
            />
          }
          title={title}
          titleTypographyProps={{ fontWeight: "bold" }}
          subheader={`${numberOfChecked(filteredItems)}/${filteredItems.length} selected`}
        />
        <Divider />
        <List
          sx={{
            width: 300,
            height: 300,
            bgcolor: "background.paper",
            overflow: "auto",
          }}
          dense
          component="div"
          role="list"
        >
          {filteredItems.map(value => {
            const labelId = `transfer-list-item-${value.id}`;

            return (
              <ListItemButton key={value.id} role="listitem" onClick={handleToggle(value)}>
                <ListItemIcon>
                  <Checkbox
                    checked={checked.some(item => item.id === value.id)}
                    tabIndex={-1}
                    disableRipple
                    inputProps={{ "aria-labelledby": labelId }}
                    sx={{ p: 0.5 }}
                  />
                </ListItemIcon>
                <ListItemText id={labelId} primary={value.name} />
              </ListItemButton>
            );
          })}
        </List>
      </Card>
    );
  };

  return (
    <>
      <Grid container spacing={2} justifyContent="center" alignItems="center" sx={{ mb: 2 }}>
        <Grid size={{ xs: 12, md: 5 }}>
          <TextField
            fullWidth
            placeholder="Search"
            id="outlined-size-small"
            size="small"
            value={filter}
            autoComplete="off"
            onChange={e => setFilter(e.target.value)}
          />
        </Grid>
      </Grid>

      <Grid container spacing={2} justifyContent="center" alignItems="center">
        <Grid size={{ xs: 12, md: 2 }}>{listPermissions("Available Permissions", left, filter)}</Grid>
        <Grid size={{ xs: 12, md: 1 }}>
          <Grid container direction="column" alignItems="center">
            <Button
              sx={{ my: 0.5 }}
              variant="outlined"
              size="small"
              onClick={handlePermissionsAdd}
              disabled={
                leftChecked.length === 0 || assignPermissionMutation.isPending || removePermissionMutation.isPending
              }
              aria-label="move selected right"
            >
              {assignPermissionMutation.isPending ? "..." : "Add"}
            </Button>

            <Button
              sx={{ my: 0.5 }}
              variant="outlined"
              size="small"
              onClick={handlePermissionsRemove}
              disabled={
                rightChecked.length === 0 || assignPermissionMutation.isPending || removePermissionMutation.isPending
              }
              aria-label="move selected left"
            >
              {removePermissionMutation.isPending ? "..." : "Remove"}
            </Button>
          </Grid>
        </Grid>
        <Grid size={{ xs: 12, md: 2 }}>{listPermissions("Assigned Permissions", right, filter)}</Grid>
      </Grid>
    </>
  );
}
