/* eslint-disable @typescript-eslint/no-explicit-any */
import { ErrorOutline } from "@mui/icons-material";
import {
  Button,
  ButtonGroup,
  Container,
  Stack,
  Typography,
} from "@mui/material";
import React from "react";

const FallBackUI: React.FC<any> = () => {
  return (
    <Container
      maxWidth="md"
      sx={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Stack spacing={2} sx={{ width: "100%" }}>
        <ErrorOutline sx={{ fontSize: "10rem", color: "primary.main" }} />
        <Typography variant="h3" gutterBottom>
          Sorry, something went wrong.
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Our team has been notified and we will get it fixed as soon as we can.
        </Typography>
        <ButtonGroup>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={() => {
              window.location.reload();
            }}
          >
            Reload page
          </Button>
          <Button
            variant="outlined"
            color="primary"
            size="large"
            onClick={() => {
              window.location.href = "/";
            }}
          >
            Take me home
          </Button>
        </ButtonGroup>
      </Stack>
    </Container>
  );
};

export default FallBackUI;
