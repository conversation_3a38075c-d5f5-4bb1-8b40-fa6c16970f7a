import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON>po<PERSON>, Stack, Avatar, Box, Button, Alert } from "@mui/material";
import { purple } from "@mui/material/colors";
import { GridCheckCircleIcon } from "@mui/x-data-grid";
import { useSelector } from "react-redux";
import { RootState } from "@/interfaces/state.interface";
import { encodeState } from "@/utils/common";
import { generateClickUpOAuthUrl } from "@/constants/oauth.constant";

import Resource from "@/core/api/resource";

const IntegrationScreen = () => {
  const userID = useSelector((state: RootState) => state.auth.user.id);
  const [clickupConnected, setClickupConnected] = useState(false);

  useEffect(() => {
    const fetchUser = async () => {
      const userResource = new Resource("users/my-profile");
      const response = await userResource.list();
      const user = response.data;
      user?.connected_service?.forEach((service: { type: { name: string; is_enable: boolean } }) => {
        if (service.type.name === "clickup") {
          setClickupConnected(service.type.is_enable);
        }
      });
    };
    fetchUser();
  }, []);

  const handleClick = () => {
    if (clickupConnected) return;
    const stateData = {
      user_id: userID,
      schema_name: "outcode",
      service_name: "clickup",
    };

    const encoded = encodeState(stateData);
    const oauthUrl = generateClickUpOAuthUrl(encoded);
    window.open(oauthUrl, "_self");
  };

  return (
    <Box sx={{ width: "100%", px: 2, mt: 2 }}>
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography fontWeight="bold">Integrations</Typography>
        <Typography variant="body2">Manage your connected services here.</Typography>
      </Alert>

      <Paper variant="outlined" sx={{ width: "100%", mb: 6 }}>
        <CardContent>
          <Stack direction="row" alignItems="center" spacing={2} py={2}>
            <Avatar
              sx={{
                bgcolor: purple[50],
                color: purple[700],
                width: 48,
                height: 48,
              }}
            >
              <GridCheckCircleIcon />
            </Avatar>
            <Box flexGrow={1}>
              <Typography fontWeight="bold">ClickUp</Typography>
              <Typography color="text.primary" fontSize="0.9rem">
                Connect to select tasks when tracking time
              </Typography>
            </Box>
            {clickupConnected ? (
              <Typography color="success.main" fontWeight="medium">
                Connected
              </Typography>
            ) : (
              <Button variant="contained" sx={{ textTransform: "none" }} onClick={handleClick}>
                Connect
              </Button>
            )}
          </Stack>
        </CardContent>
      </Paper>
    </Box>
  );
};
export default IntegrationScreen;
