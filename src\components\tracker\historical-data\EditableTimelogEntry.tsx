import React, { useState, useMemo, useRef, useEffect, useCallback } from "react";
import {
  ListItem,
  Box,
  TextField,
  Autocomplete,
  IconButton,
  Tooltip,
  Typography,
  Paper,
  CircularProgress,
  Chip,
} from "@mui/material";
import {
  Attach<PERSON>oney as AttachMoneyIcon,
  AccessTime as AccessTimeIcon,
  Task as TaskIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayArrowIcon,
} from "@mui/icons-material";
import { format, parse, format as formatDate, isValid as isValidDate, parseISO } from "date-fns";
import { EditableTimelogEntryProps, EditData } from "@/types/tracker";
import {
  useClickupTasks,
  useClickupTaskById,
  useUpdateTimelog,
  useDeleteTimelog,
  useUserProfile,
} from "@/hooks/tracker";
import { useProjects } from "@/hooks/tracker/useProjects";
import { formatTime } from "@/utils/time";
import { useDebounce } from "@/hooks/tracker";
import UpdateConfirmationModal from "@/components/tracker/UpdateConfirmationModal";
import DeleteConfirmationDialog from "@/components/tracker/DeleteConfirmationDialog";
import ToastMessage from "@/components/toast/toast.component";
import isEqual from "lodash/isEqual";

const EditableTimelogEntry: React.FC<EditableTimelogEntryProps> = ({ log, onRestartTimer, isTracking }) => {
  const [isEditing, setIsEditing] = useState(false);

  const isCurrentWeek = useMemo(() => {
    if (!log.start_time) return false;
    const entryDate = new Date(log.start_time);
    const now = new Date();

    const currentMonday = new Date(now);
    currentMonday.setHours(0, 0, 0, 0);
    currentMonday.setDate(now.getDate() - ((now.getDay() + 6) % 7));

    const currentSunday = new Date(currentMonday);
    currentSunday.setDate(currentMonday.getDate() + 6);
    return entryDate >= currentMonday && entryDate <= currentSunday;
  }, [log.start_time]);
  const [editData, setEditData] = useState({
    project: log.project,
    description: (log.description || "").trim(),
    start_time: log.start_time,
    end_time: log.end_time || "",
    is_billable: log.is_billable,
    is_ot: log.is_ot,
    clickup_task_id: log.clickup_task_id || "",
  });

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [startError, setStartError] = useState<string>("");
  const [endError, setEndError] = useState<string>("");
  const [startEndError, setStartEndError] = useState<string>("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const getCurrentWeekRange = () => {
    const now = new Date();
    const monday = new Date(now);
    monday.setHours(0, 0, 0, 0);
    monday.setDate(now.getDate() - ((now.getDay() + 6) % 7));
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);
    sunday.setHours(23, 59, 59, 999);

    const max = now > sunday ? sunday : now;
    return { monday, max };
  };
  const { monday: weekMinDate, max: weekMaxDate } = getCurrentWeekRange();
  const weekMinStr = format(weekMinDate, "yyyy-MM-dd'T'00:00");
  const weekMaxStr = format(weekMaxDate, "yyyy-MM-dd'T'HH:mm");

  const projectDebounce = useDebounce(300);
  const taskDebounce = useDebounce(300);
  const { projectOptions, isLoading: projectsLoading } = useProjects(projectDebounce.debouncedValue);
  const { data: userProfile } = useUserProfile();
  const updateTimelogMutation = useUpdateTimelog();
  const deleteTimelogMutation = useDeleteTimelog();
  const { data: allProjectsData } = useProjects("", {
    ids: log.project ? [log.project] : [],
  });

  const isClickUpConnected =
    userProfile?.connected_service?.some(service => service.type.name === "clickup" && service.type.is_enable) ?? false;

  const validateStartEndTime = useCallback(() => {
    if (!editData.start_time || !editData.end_time) return "";

    const startDate = new Date(editData.start_time);
    const endDate = new Date(editData.end_time);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return "Invalid date format";
    }

    if (startDate >= endDate) {
      return "Start time must be before end time";
    }

    return "";
  }, [editData.start_time, editData.end_time]);

  useEffect(() => {
    if (editData.start_time && editData.end_time) {
      setStartEndError(validateStartEndTime());
    } else {
      setStartEndError("");
    }
  }, [editData.start_time, editData.end_time, validateStartEndTime]);

  const selectedProject = useMemo(
    () => projectOptions.find((p: { value: string }) => p.value === editData.project) || null,
    [projectOptions, editData.project]
  );

  const fullSelectedProject = useMemo(() => {
    if (!allProjectsData?.pages || !editData.project) return null;
    return allProjectsData.pages.flatMap(page => page.results).find(p => p.id === editData.project);
  }, [allProjectsData, editData.project]);

  const previousProject = useMemo(() => {
    if (!allProjectsData?.pages || !log.project) return null;
    const project = allProjectsData.pages.flatMap(page => page.results).find(p => p.id === log.project);
    return project || null;
  }, [allProjectsData, log.project]);

  const {
    taskOptions: currentTaskOptions = [],
    isLoading: tasksLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    error: tasksError,
    retry: retryTasks,
  } = useClickupTasks(fullSelectedProject?.clickup_space_id, taskDebounce.debouncedValue, 10);

  const { task: specificTask } = useClickupTaskById(
    log.clickup_task_id && fullSelectedProject?.clickup_space_id ? fullSelectedProject.clickup_space_id : null,
    log.clickup_task_id || null
  );

  const shouldFetchPreviousTasks =
    previousProject?.clickup_space_id &&
    previousProject.clickup_space_id !== fullSelectedProject?.clickup_space_id &&
    log.clickup_task_id;

  const { taskOptions: previousTaskOptions = [] } = useClickupTasks(
    shouldFetchPreviousTasks ? previousProject?.clickup_space_id : null,
    "",
    10
  );

  const clickupTaskOptions = useMemo(() => {
    const options = [...currentTaskOptions];
    const currentValue = editData.clickup_task_id;

    if (currentValue && currentValue !== "" && !options.find(option => option.value === currentValue)) {
      const taskLabel =
        specificTask && specificTask.task_id === currentValue ? specificTask.name : `Task ID: ${currentValue}`;
      const taskStatus = specificTask && specificTask.task_id === currentValue ? specificTask.status : undefined;

      options.unshift({
        label: taskLabel,
        value: currentValue,
        status: taskStatus,
      });
    }

    if (isFetchingNextPage && hasNextPage) {
      options.push({
        label: "Loading more tasks...",
        value: "__loading__",
        isPending: true,
      });
    }

    return options;
  }, [currentTaskOptions, editData.clickup_task_id, specificTask, isFetchingNextPage, hasNextPage]);

  const handleListboxScroll = useCallback(
    (event: React.SyntheticEvent) => {
      const listboxNode = event.currentTarget as HTMLElement;
      const { scrollTop, scrollHeight, clientHeight } = listboxNode;

      if (scrollHeight - scrollTop <= clientHeight + 20) {
        if (hasNextPage && !isFetchingNextPage && fetchNextPage) {
          fetchNextPage();
        }
      }
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage]
  );

  interface ClickupTaskOption {
    label: string;
    value: string;
    status?: string;
    isPending?: boolean;
  }

  const renderClickupOption = useCallback(
    (props: React.HTMLAttributes<HTMLLIElement>, option: ClickupTaskOption) => (
      <li {...props} key={option.value}>
        {option.isPending ? (
          <Box display="flex" alignItems="center" justifyContent="center" p={1}>
            <CircularProgress size={16} />
            <Typography variant="caption" sx={{ ml: 1 }}>
              {option.label}
            </Typography>
          </Box>
        ) : (
          <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
            <Typography>{option.label}</Typography>
            {option.status && (
              <Chip
                label={option.status.toUpperCase()}
                size="small"
                variant="filled"
                sx={{ fontSize: "0.75rem", height: "20px" }}
              />
            )}
          </Box>
        )}
      </li>
    ),
    []
  );

  const clickupNoOptionsText = useMemo(() => {
    if (tasksError) {
      return (
        <Box p={2}>
          <Typography color="error" variant="body2" sx={{ mb: 1 }}>
            Failed to load tasks
          </Typography>
          {retryTasks && (
            <Typography
              variant="caption"
              sx={{ cursor: "pointer", textDecoration: "underline" }}
              onClick={() => retryTasks()}
            >
              Retry
            </Typography>
          )}
        </Box>
      );
    }

    if (isFetchingNextPage) {
      return (
        <Box display="flex" alignItems="center" justifyContent="center" p={1}>
          <CircularProgress size={20} />
          <Typography sx={{ ml: 1 }}>Loading tasks...</Typography>
        </Box>
      );
    }

    return "No tasks found";
  }, [tasksError, retryTasks, isFetchingNextPage]);

  const projectName = useMemo(() => {
    const project = projectOptions.find((p: { value: string }) => p.value === log.project);
    return project?.label || "Unknown Project";
  }, [projectOptions, log.project]);

  const duration = useMemo(() => {
    if (!log.end_time) return "Running";
    try {
      const start = new Date(log.start_time);
      const end = new Date(log.end_time);
      const diffInSeconds = Math.floor((end.getTime() - start.getTime()) / 1000);
      return formatTime(diffInSeconds);
    } catch {
      return "Invalid";
    }
  }, [log.start_time, log.end_time]);

  const formatTimeForDisplay = (timeString?: string) => {
    if (!timeString) return "Running";
    try {
      return format(new Date(timeString), "HH:mm");
    } catch {
      return "Invalid";
    }
  };

  const formatTimeForInput = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return format(date, "yyyy-MM-dd'T'HH:mm");
    } catch {
      return "";
    }
  };

  const parseTimeFromInput = (inputValue: string) => {
    try {
      const date = parse(inputValue, "yyyy-MM-dd'T'HH:mm", new Date());
      return date.toISOString();
    } catch {
      return inputValue;
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditData({
      project: log.project,
      description: (log.description || "").trim(),
      start_time: log.start_time,
      end_time: log.end_time || "",
      is_billable: log.is_billable,
      is_ot: log.is_ot,
      clickup_task_id: log.clickup_task_id || "",
    });

    setStartError("");
    setEndError("");
    setStartEndError("");
  };

  const handleSave = () => {
    const startErr = validateDateInCurrentWeek(editData.start_time, "Start time");
    const endErr = validateDateInCurrentWeek(editData.end_time, "End time");
    const startEndErr = validateStartEndTime();

    setStartError(startErr);
    setEndError(endErr);
    setStartEndError(startEndErr);

    if (startErr || endErr || startEndErr) return;

    setEditData(prev => ({
      ...prev,
      description: prev.description.trim(),
    }));
    setShowConfirmation(true);
  };

  const getChangedFields = (oldData: EditData, newData: EditData) => {
    const changed: Partial<EditData> = {};
    (Object.keys(newData) as (keyof EditData)[]).forEach(key => {
      if (key === "id" || key === "created_at" || key === "updated_at" || key === "user") return;
      if (!isEqual(oldData[key], newData[key])) {
        changed[key] = newData[key];
      }
    });
    return changed;
  };

  const originalLogRef = useRef({
    project: log.project,
    description: (log.description || "").trim(),
    start_time: log.start_time,
    end_time: log.end_time || "",
    is_billable: log.is_billable,
    is_ot: log.is_ot,
    clickup_task_id: log.clickup_task_id || "",
    id: log.id,
    user: log.user,
  });

  useEffect(() => {
    originalLogRef.current = {
      project: log.project,
      description: (log.description || "").trim(),
      start_time: log.start_time,
      end_time: log.end_time || "",
      is_billable: log.is_billable,
      is_ot: log.is_ot,
      clickup_task_id: log.clickup_task_id || "",
      id: log.id,
      user: log.user,
    };
  }, [
    log.id,
    log.project,
    log.description,
    log.start_time,
    log.end_time,
    log.is_billable,
    log.is_ot,
    log.clickup_task_id,
    log.user,
  ]);

  const handleConfirmSave = () => {
    setShowConfirmation(false);

    const oldData = originalLogRef.current;
    const newData = {
      ...editData,
      description: editData.description.trim(),
    };
    const changedFields = getChangedFields(oldData, newData);
    if (Object.keys(changedFields).length === 0) {
      setIsEditing(false);
      return;
    }
    updateTimelogMutation.mutate(
      { timelogId: log.id, updateData: changedFields },
      {
        onSuccess: () => {
          setIsEditing(false);
        },
      }
    );
  };

  const handleCancelSave = () => {
    setShowConfirmation(false);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = () => {
    deleteTimelogMutation.mutate(log.id);
    setShowDeleteDialog(false);
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  const handleRestartTimer = async () => {
    if (!onRestartTimer) return;

    try {
      const formValues = {
        project: log.project,
        clickupTaskName: log.clickup_task_id || "",
        description: log.description || "",
        isBillable: log.is_billable,
        isOT: log.is_ot,
      };

      await onRestartTimer(formValues);
    } catch {
      ToastMessage.error("Failed to restart timer. Please try again.");
    }
  };

  const getProjectLabel = (id: string): string => {
    if (!allProjectsData?.pages) return id;
    const project = allProjectsData.pages.flatMap(page => page.results).find(p => p.id === id);
    return project?.name || id;
  };

  const getTaskLabelFromList = (id: string, taskList: typeof previousTaskOptions) => {
    const task = taskList.find((t: { value: string }) => t.value === id);
    return task ? task.label : id;
  };

  const formatValueForDialog = (field: string, value: unknown): string => {
    if (field === "project" && typeof value === "string") {
      return getProjectLabel(value);
    }
    if (field === "clickup_task_id" && typeof value === "string") {
      let taskName = value;

      const currentTask = getTaskLabelFromList(value, currentTaskOptions);
      if (currentTask !== value) {
        taskName = currentTask;
      } else if (previousTaskOptions.length > 0) {
        const previousTask = getTaskLabelFromList(value, previousTaskOptions);
        if (previousTask !== value) {
          taskName = previousTask;
        }
      } else if (specificTask && specificTask.task_id === value) {
        taskName = specificTask.name;
      }

      return taskName;
    }
    if ((field === "start_time" || field === "end_time") && typeof value === "string") {
      const date = parseISO(value);
      if (isValidDate(date)) {
        return formatDate(date, "yyyy-MM-dd HH:mm");
      }
      return value;
    }
    if (typeof value === "boolean") return value ? "Yes" : "No";
    if (typeof value === "number") return value.toString();
    if (typeof value === "string") return value;
    if (value === null || value === undefined) return "";
    const stringified = JSON.stringify(value);
    return stringified || "";
  };

  const validateDateInCurrentWeek = (dateString: string, label: string) => {
    if (!dateString) return "";
    const { monday, max } = getCurrentWeekRange();
    const date = new Date(dateString);
    if (date < monday || date > max) {
      return `${label} must be within the current week and not in the future (${monday.toLocaleDateString()} - ${max.toLocaleString()})`;
    }
    return "";
  };

  return (
    <>
      {isEditing ? (
        <ListItem
          sx={{
            flexDirection: "column",
            alignItems: "stretch",
            bgcolor: "action.hover",
            borderRadius: 1,
            mb: 1,
          }}
        >
          <Paper elevation={1} sx={{ p: 2 }}>
            <Box display="flex" flexDirection="column" gap={2}>
              {}
              <Box
                display="flex"
                flexDirection={{ xs: "column", sm: "row" }}
                gap={2}
                flexWrap={{ xs: "wrap", sm: "nowrap" }}
              >
                <TextField
                  label="Start Time"
                  type="datetime-local"
                  value={formatTimeForInput(editData.start_time)}
                  onChange={e => {
                    const newValue = parseTimeFromInput(e.target.value);
                    setEditData(prev => ({
                      ...prev,
                      start_time: newValue,
                    }));
                    setStartError(validateDateInCurrentWeek(newValue, "Start time"));
                  }}
                  error={!!startError}
                  helperText={startError}
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  sx={{ minWidth: { xs: 0, sm: 200 }, flex: 1 }}
                  inputProps={{ min: weekMinStr, max: weekMaxStr }}
                />
                <TextField
                  label="End Time"
                  type="datetime-local"
                  value={formatTimeForInput(editData.end_time)}
                  onChange={e => {
                    const newValue = parseTimeFromInput(e.target.value);
                    setEditData(prev => ({
                      ...prev,
                      end_time: newValue,
                    }));
                    setEndError(validateDateInCurrentWeek(newValue, "End time"));
                  }}
                  error={!!endError}
                  helperText={endError}
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  sx={{ minWidth: { xs: 0, sm: 200 }, flex: 1 }}
                  inputProps={{ min: weekMinStr, max: weekMaxStr }}
                />
              </Box>
              {}
              {startEndError && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="body2" color="error">
                    {startEndError}
                  </Typography>
                </Box>
              )}
              <Box
                display="flex"
                flexDirection={{ xs: "column", sm: "row" }}
                gap={2}
                flexWrap={{ xs: "wrap", sm: "nowrap" }}
              >
                <Autocomplete
                  options={projectOptions}
                  getOptionLabel={option => option.label}
                  value={selectedProject}
                  onChange={(_, newValue) => {
                    setEditData(prev => ({
                      ...prev,
                      project: newValue?.value || "",
                    }));

                    if (newValue?.value !== selectedProject?.value) {
                      setEditData(prev => ({ ...prev, clickup_task_id: "" }));
                    }
                  }}
                  onInputChange={(_, value) => {
                    projectDebounce.setDebouncedValue(value);
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label="Project"
                      size="small"
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {projectsLoading ? <CircularProgress size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                  sx={{ minWidth: { xs: 0, sm: 200 }, flex: 1 }}
                  disabled={projectsLoading}
                />
                {isClickUpConnected && selectedProject?.space_id && (
                  <Autocomplete
                    options={clickupTaskOptions}
                    getOptionLabel={option => option.label}
                    value={
                      currentTaskOptions.find(
                        (t: { value: string }) => t.value === editData.clickup_task_id && t.value !== "__loading__"
                      ) ||
                      (editData.clickup_task_id
                        ? {
                            label:
                              specificTask && specificTask.task_id === editData.clickup_task_id
                                ? specificTask.name
                                : `Task ID: ${editData.clickup_task_id}`,
                            value: editData.clickup_task_id,
                          }
                        : null)
                    }
                    onChange={(_, newValue) => {
                      if (newValue?.value === "__loading__") return;

                      setEditData(prev => ({
                        ...prev,
                        clickup_task_id: newValue?.value || "",
                      }));
                    }}
                    onInputChange={(_, value) => {
                      taskDebounce.setDebouncedValue(value);
                    }}
                    ListboxProps={{
                      onScroll: handleListboxScroll,
                      style: { maxHeight: 300 },
                    }}
                    renderOption={renderClickupOption}
                    renderInput={params => (
                      <TextField
                        {...params}
                        label="ClickUp Task"
                        size="small"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {tasksLoading ? <CircularProgress size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                    noOptionsText={clickupNoOptionsText}
                    sx={{ minWidth: { xs: 0, sm: 200 }, flex: 1 }}
                    disabled={!selectedProject || tasksLoading}
                  />
                )}
              </Box>
              {}
              <TextField
                label="Description"
                multiline
                rows={2}
                value={editData.description}
                onChange={e =>
                  setEditData(prev => ({
                    ...prev,
                    description: e.target.value.trimStart(),
                  }))
                }
                fullWidth
                size="small"
                inputProps={{ maxLength: 500 }}
              />
              {}
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box display="flex" gap={1}>
                  <Tooltip title="Toggle Billable">
                    <IconButton
                      color={editData.is_billable ? "primary" : "default"}
                      onClick={() =>
                        setEditData(prev => ({
                          ...prev,
                          is_billable: !prev.is_billable,
                        }))
                      }
                      size="small"
                    >
                      <AttachMoneyIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Toggle Overtime">
                    <IconButton
                      color={editData.is_ot ? "primary" : "default"}
                      onClick={() => setEditData(prev => ({ ...prev, is_ot: !prev.is_ot }))}
                      size="small"
                    >
                      <AccessTimeIcon />
                    </IconButton>
                  </Tooltip>
                </Box>

                <Box display="flex" gap={1}>
                  <Tooltip title="Cancel">
                    <IconButton onClick={handleCancel} size="small">
                      <CancelIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Save">
                    <IconButton
                      onClick={handleSave}
                      size="small"
                      color="primary"
                      disabled={updateTimelogMutation.isPending}
                    >
                      {updateTimelogMutation.isPending ? <CircularProgress size={20} /> : <SaveIcon />}
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
            </Box>
          </Paper>
        </ListItem>
      ) : (
        <ListItem
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            alignItems: { sm: "center" },
            justifyContent: "space-between",
            gap: 1,
            p: 1,
            borderRadius: 1,
            bgcolor: "background.paper",
          }}
        >
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" color="text.secondary">
              {projectName}
            </Typography>
            <Typography variant="body1">{log.description || "No description"}</Typography>
          </Box>

          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <Box sx={{ display: "flex", gap: 1 }}>
              <Tooltip title={log.is_billable ? "Billable" : "Non-billable"}>
                <AttachMoneyIcon color={log.is_billable ? "primary" : "disabled"} fontSize="small" />
              </Tooltip>
              {log.clickup_task_id && (
                <Tooltip title="ClickUp Task">
                  <TaskIcon color="primary" fontSize="small" />
                </Tooltip>
              )}
              <Tooltip title={log.is_ot ? "Overtime" : "Regular time"}>
                <AccessTimeIcon color={log.is_ot ? "primary" : "disabled"} fontSize="small" />
              </Tooltip>
            </Box>
            <Typography variant="body2" color="text.secondary">
              {formatTimeForDisplay(log.start_time)} - {formatTimeForDisplay(log.end_time)}
            </Typography>
            <Typography variant="body2">{duration}</Typography>
            {!isEditing && (
              <Box display="flex" gap={0.5}>
                <Tooltip title={isCurrentWeek ? "Edit" : "Editing allowed only for entries in the current week"}>
                  <span>
                    <IconButton onClick={handleEdit} size="small" disabled={!isCurrentWeek}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip
                  title={
                    !onRestartTimer
                      ? "Restart feature not available"
                      : !isCurrentWeek
                        ? "Restart allowed only for entries in the current week"
                        : isTracking
                          ? "Stop current timer before restarting"
                          : "Restart timer with same values"
                  }
                >
                  <span>
                    <IconButton
                      onClick={handleRestartTimer}
                      size="small"
                      color="primary"
                      disabled={!onRestartTimer || !isCurrentWeek || !!isTracking}
                    >
                      <PlayArrowIcon fontSize="small" />
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip title="Delete">
                  <IconButton onClick={handleDelete} disabled={!isCurrentWeek} size="small" color="error">
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </Box>
        </ListItem>
      )}
      <UpdateConfirmationModal
        open={showConfirmation}
        onClose={handleCancelSave}
        onConfirm={handleConfirmSave}
        oldData={originalLogRef.current}
        newData={{
          ...editData,
          description: editData.description.trim(),
          id: log.id,
          user: log.user,
        }}
        loading={updateTimelogMutation.isPending}
        formatValue={(field, value) => formatValueForDialog(field, value)}
      />
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        loading={deleteTimelogMutation.isPending}
      />
    </>
  );
};

export default EditableTimelogEntry;
