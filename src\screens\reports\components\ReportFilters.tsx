import React, { useState, useEffect } from "react";
import { Box, Switch, FormControlLabel, SelectChangeEvent } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { isValid, isAfter } from "date-fns";
import AutocompleteInfinite from "@/components/common/AutocompleteInfinite";
import { useProjects } from "@/hooks/tracker/useProjects";
import { useUsersInfinite } from "@/hooks/tracker/useUsersInfinite";
import { ProjectOption } from "@/types/tracker";
import { ReportFiltersProps, UserOption, DateValidationState } from "@/types/reports";

// Move hook wrappers outside component for performance
const useProjectOptions = (searchTerm: string) => {
  const { projectOptions, ...rest } = useProjects(searchTerm);
  return { options: projectOptions, ...rest };
};

const useUserOptions = (searchTerm: string) => {
  const { userOptions, ...rest } = useUsersInfinite(searchTerm);
  return { options: userOptions, ...rest };
};

const ReportFilters: React.FC<ReportFiltersProps> = ({
  dateRange,
  setDateRange,
  selectedProjects,
  handleProjectsChange,
  selectedUsers,
  handleUsersChange,
  billableFilter,
  setBillableFilter,
  otFilter,
  setOtFilter,
  onValidationChange,
}) => {
  const [dateErrors, setDateErrors] = useState<DateValidationState>({
    startDate: false,
    endDate: false,
    dateRangeError: false,
  });

  // Notify parent component when validation state changes
  useEffect(() => {
    const hasErrors = dateErrors.startDate || dateErrors.endDate || dateErrors.dateRangeError;
    onValidationChange?.(!hasErrors);
  }, [dateErrors, onValidationChange]);

  // Adapter functions to transform AutocompleteInfinite's newValue to SelectChangeEvent-like format
  const handleProjectsAutocompleteChange = (newValue: string[]) => {
    handleProjectsChange({ target: { value: newValue } } as SelectChangeEvent<string[]>);
  };
  const handleUsersAutocompleteChange = (newValue: string[]) => {
    handleUsersChange({ target: { value: newValue } } as SelectChangeEvent<string[]>);
  };

  // Date validation handlers
  const handleStartDateChange = (newValue: Date | null) => {
    if (newValue && isValid(newValue)) {
      const newRange = { ...dateRange, start: newValue };
      const hasRangeError = isAfter(newValue, dateRange.end);

      setDateRange(newRange);
      setDateErrors({
        ...dateErrors,
        startDate: false,
        dateRangeError: hasRangeError,
      });
    } else if (newValue === null) {
      // If user clears the field, keep the current value
      setDateErrors({ ...dateErrors, startDate: false });
      return;
    } else {
      // Invalid date entered
      setDateErrors({ ...dateErrors, startDate: true });
    }
  };

  const handleEndDateChange = (newValue: Date | null) => {
    if (newValue && isValid(newValue)) {
      const newRange = { ...dateRange, end: newValue };
      const hasRangeError = isAfter(dateRange.start, newValue);

      setDateRange(newRange);
      setDateErrors({
        ...dateErrors,
        endDate: false,
        dateRangeError: hasRangeError,
      });
    } else if (newValue === null) {
      // If user clears the field, keep the current value
      setDateErrors({ ...dateErrors, endDate: false });
      return;
    } else {
      // Invalid date entered
      setDateErrors({ ...dateErrors, endDate: true });
    }
  };

  // Responsive layout: allow wrapping and balance autocomplete boxes
  return (
    <Box sx={{ display: "flex", gap: 2, mb: 3, alignItems: "center", flexWrap: "wrap" }}>
      <DatePicker
        label="Start Date"
        value={dateRange.start}
        onChange={handleStartDateChange}
        sx={{ minWidth: 180 }}
        slotProps={{
          textField: {
            error: dateErrors.startDate || dateErrors.dateRangeError,
            helperText: dateErrors.startDate
              ? "Invalid date"
              : dateErrors.dateRangeError
                ? "Start date must be before end date"
                : undefined,
          },
        }}
      />
      <DatePicker
        label="End Date"
        value={dateRange.end}
        onChange={handleEndDateChange}
        sx={{ minWidth: 180 }}
        slotProps={{
          textField: {
            error: dateErrors.endDate || dateErrors.dateRangeError,
            helperText: dateErrors.endDate
              ? "Invalid date"
              : dateErrors.dateRangeError
                ? "End date must be after start date"
                : undefined,
          },
        }}
      />
      <Box sx={{ minWidth: 220, flexGrow: 1, flexBasis: 0 }}>
        <AutocompleteInfinite<ProjectOption>
          value={selectedProjects}
          onChange={handleProjectsAutocompleteChange}
          useInfiniteOptions={useProjectOptions}
          label="Projects"
          placeholder="Search projects..."
        />
      </Box>
      <Box sx={{ minWidth: 220, flexGrow: 1, flexBasis: 0 }}>
        <AutocompleteInfinite<UserOption>
          value={selectedUsers}
          onChange={handleUsersAutocompleteChange}
          useInfiniteOptions={useUserOptions}
          label="Users"
          placeholder="Search users..."
        />
      </Box>
      <FormControlLabel
        control={<Switch checked={billableFilter} onChange={e => setBillableFilter(e.target.checked)} />}
        label="Billable Only"
      />
      <FormControlLabel
        control={<Switch checked={otFilter} onChange={e => setOtFilter(e.target.checked)} />}
        label="OT Only"
      />
    </Box>
  );
};

export default ReportFilters;
