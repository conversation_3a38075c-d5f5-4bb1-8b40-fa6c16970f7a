import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Link,
  InputAdornment,
  IconButton,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import Resource from "@/core/api/resource";
import ToastMessage from "@/components/toast/toast.component";
import { useMutation } from "@tanstack/react-query";
import LoginWrapper from "@/components/login-wrapper/login-wrapper.component";
import GenericFormControl from "@/components/form-control/generic-form.component";

const verifyTokenResource = new Resource("auth/token/user/verify");

interface ICreatePassword {
  password: string;
  confirm_password: string;
}
interface IsetPasswordMutation {
  uid: string;
  token: string;
  user: ICreatePassword;
}

const validationSchema = Yup.object({
  password: Yup.string().min(6, "Password must be at least 6 characters").required("Password is required"),
  confirm_password: Yup.string()
    .oneOf([Yup.ref("password")], "Passwords must match")
    .required("Confirm Password is required"),
});

const SetPassword: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const navigate = useNavigate();
  const { uid, token } = useParams<{ uid: string; token: string }>();
  const [searchParams] = useSearchParams();
  const name = searchParams.get("name") || "there";

  const userResource = new Resource("users/invite/register");

  const [show, setShow] = useState({
    showPassword: false,
    showConfirmPassword: false,
  });
  const [linkExpired, setLinkExpired] = useState(false);
  const [checking, setChecking] = useState(true);

  const { mutate: storePasswordMutation, isPending: storePasswordPending } = useMutation({
    mutationKey: ["storePassword"],
    mutationFn: async ({ uid, token, user }: IsetPasswordMutation) => {
      return await userResource.store({ uid, token, user });
    },
    onSuccess: () => {
      ToastMessage.success("Password set successfully.");
      navigate("/login");
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  const formik = useFormik({
    initialValues: {
      password: "",
      confirm_password: "",
    },
    validationSchema,
    onSubmit: values => {
      if (!uid || !token) {
        ToastMessage.error("Something went wrong. Please try again.");
        return;
      }
      storePasswordMutation({
        uid,
        token,
        user: values,
      });
    },
  });

  useEffect(() => {
    const checkToken = async () => {
      try {
        await verifyTokenResource.store({ uid, token });
        setLinkExpired(false);
      } catch {
        setLinkExpired(true);
      } finally {
        setChecking(false);
      }
    };
    if (uid && token) {
      checkToken();
    }
  }, [uid, token]);

  // For toggling password visibility:
  const handleToggleShow = (field: "showPassword" | "showConfirmPassword") => {
    setShow(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  if (checking) {
    return <div>Loading...</div>;
  }

  if (linkExpired) {
    return (
      <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center" flexDirection="column">
        <img src="/assets/svg/expired-svg.svg" alt="Link expired" style={{ width: 120, marginBottom: 16 }} />
        <Typography variant="h4" color="error" gutterBottom>
          Link Expired
        </Typography>
        <Typography variant="body1">This invite link has already been used or is expired.</Typography>
        <Button
          variant="contained"
          href="/login"
          sx={{
            textTransform: "none",
            bgcolor: theme.palette.custom?.brand?.azureRadiance || theme.palette.primary.main,
            "&:hover": {
              bgcolor: theme.palette.custom?.brand?.azureRadiance
                ? theme.palette.custom.brand.azureRadiance + "CC"
                : theme.palette.primary.dark,
            },
            fontWeight: 600,
            px: 4,
            py: 1,
          }}
        >
          Back to Login
        </Button>
      </Box>
    );
  }

  return (
    <LoginWrapper>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          px: 2,
          py: 4,
        }}
      >
        <Card
          sx={{
            width: "100%",
            maxWidth: 420,
            boxShadow: 3,
            borderRadius: 2,
          }}
        >
          <CardContent>
            <Typography variant={isMobile ? "h5" : "h2"} gutterBottom fontWeight={600} textAlign="center">
              Hi {name}, Welcome
            </Typography>
            <Typography variant="body2" sx={{ mb: 2 }} textAlign="center" color="text.primary">
              Please set your password to complete the registration process.
            </Typography>

            <form onSubmit={formik.handleSubmit} noValidate>
              <GenericFormControl
                id="password"
                label="Create Password"
                type={show.showPassword ? "text" : "password"}
                value={formik.values.password}
                onChangeFn={formik.handleChange}
                error={formik.touched.password && !!formik.errors.password}
                helperText={formik.touched.password ? formik.errors.password : undefined}
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => handleToggleShow("showPassword")}
                      edge="end"
                    >
                      {show.showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                }
              />

              <GenericFormControl
                id="confirm_password"
                label="Confirm Password"
                type={show.showConfirmPassword ? "text" : "password"}
                value={formik.values.confirm_password}
                onChangeFn={formik.handleChange}
                error={formik.touched.confirm_password && !!formik.errors.confirm_password}
                helperText={formik.touched.confirm_password ? formik.errors.confirm_password : undefined}
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => handleToggleShow("showConfirmPassword")}
                      edge="end"
                    >
                      {show.showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                }
              />

              <Button
                fullWidth
                variant="contained"
                sx={{
                  bgcolor: theme.palette.custom.brand.azureRadiance,
                  textTransform: "none",
                  fontWeight: 600,
                  py: 1.2,
                  mt: 3,
                }}
                type="submit"
                disabled={storePasswordPending}
              >
                {storePasswordPending ? "Setting Password..." : "Set Password"}
              </Button>
            </form>

            <Box sx={{ textAlign: "center", mt: 3 }}>
              <Link href="/login" underline="none" fontSize="0.85rem">
                Back to Login
              </Link>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </LoginWrapper>
  );
};

export default SetPassword;
