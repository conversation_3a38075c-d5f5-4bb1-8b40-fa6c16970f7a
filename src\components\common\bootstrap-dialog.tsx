import {
  Dialog,
  DialogProps,
  styled,
  DialogTitle,
  DialogContent,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React from "react";

interface BootstrapDialogProps extends DialogProps {
  title: string;
  onClose: () => void;
  children: React.ReactNode;
}

const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

const BootstrapDialogBox: React.FC<BootstrapDialogProps> = ({
  title,
  onClose,
  children,
  ...dialogProps
}) => {
  return (
    <StyledDialog {...dialogProps}>
      <DialogTitle sx={{ m: 0, p: 2 }}>
        {title}
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={(theme) => ({
            position: "absolute",
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>{children}</DialogContent>
    </StyledDialog>
  );
};

export default BootstrapDialogBox;
