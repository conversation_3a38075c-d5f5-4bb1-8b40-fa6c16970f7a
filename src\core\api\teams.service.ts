import http from "../http/http";
import Resource from "./resource";

class TeamResources extends Resource {
  constructor() {
    super("users");
  }
}

class TeamGroupResources extends Resource {
  constructor() {
    super("users/groups");
  }
}

class TeamInviteResources extends Resource {
  constructor() {
    super("users/invite");
  }
}
class TeamUsersResources extends Resource {
  constructor() {
    super("users/my-profile");
  }
}

class TeamUserProfileResources extends Resource {
  constructor() {
    super("users/info");
  }

  uploadImage(resource: unknown) {
    return http({
      url: "/" + this.uri + "/",

      method: "patch",
      data: resource,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
}

export default {
  TeamResources,
  TeamGroupResources,
  TeamInviteResources,
  TeamUsersResources,
  TeamUserProfileResources,
};
