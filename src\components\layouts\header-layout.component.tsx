import { AppBar, Toolbar } from "@mui/material";
import Box from "@mui/material/Box";
import { Outlet } from "react-router-dom";
import Search from "@/assets/images/SearchLogo.png";
import User from "@/assets/images/UserLogo.png";
import Typography from "@mui/material/Typography";
import Footer from "@/components/footer/footer.component";

const HeaderLayout = () => {
  return (
    <>
      <AppBar
        position="static"
        sx={{ bgcolor: "custom.brand.lime", color: "custom.brand.charcoal" }}
      >
        <Toolbar
          sx={{
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            Logo
          </Box>
          <Typography
            variant="h6"
            sx={{
              fontWeight: "bold",
              fontSize: "18px",
              textAlign: "center",
              flexGrow: 1,
            }}
          >
            Free Time
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Box
              component="img"
              alt="Icon"
              src={Search}
              sx={{ width: "24px", height: "24px" }}
            />
            <Box component="img" alt="Icon" src={User} sx={{ ml: 2 }} />
          </Box>
        </Toolbar>
      </AppBar>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "left",
          mb: "56px",
        }}
      >
        <Outlet />
      </Box>
      <Footer />
    </>
  );
};

export default HeaderLayout;
