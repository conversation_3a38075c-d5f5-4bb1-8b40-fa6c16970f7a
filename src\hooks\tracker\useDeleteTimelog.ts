import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import ToastMessage from "@/components/toast/toast.component";

export const useDeleteTimelog = () => {
  const timelogResource = useMemo(() => new Resource("timelogs"), []);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (timelogId: string) => {
      return timelogResource.destroy(timelogId);
    },
    onSuccess: () => {
      // Invalidate both queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: ["timelogs"] });
      queryClient.invalidateQueries({ queryKey: ["weeklyTimelogs"] });
      ToastMessage.success("Time entry deleted successfully");
    },
    onError: () => {
      ToastMessage.error("Failed to delete time entry. Please try again.");
    },
  });
};
