import React from "react";
import { List, ListItem, ListItemText, Typography, Paper } from "@mui/material";
import { IPermission, IPermissionProps } from "@/types";

const PermissionList: React.FC<IPermissionProps> = ({ permissions }) => {
  return (
    <Paper elevation={2} sx={{ maxWidth: 600, margin: "auto", p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Available Permissions
      </Typography>
      <List dense>
        {permissions.map((permission: IPermission) => (
          <ListItem key={permission.id}>
            <ListItemText primary={permission.name} />
          </ListItem>
        ))}
      </List>
    </Paper>
  );
};

export default PermissionList;
