import React from "react";
import { Box } from "@mui/material";

interface FieldBoxProps {
  children: React.ReactNode;
  flex?: number;
  minWidth?: number;
}

const FieldBox: React.FC<FieldBoxProps> = ({
  children,
  flex = 1,
  minWidth = 200,
}) => (
  <Box
    sx={{
      flexGrow: flex,
      minWidth,
      boxSizing: "border-box",
      display: "flex",
      flexDirection: "column",
    }}
  >
    {children}
  </Box>
);

export default FieldBox;
