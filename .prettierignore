# Dependencies
node_modules/
.yarn/
yarn.lock
package-lock.json

# Build outputs
dist/
build/
coverage/

# Generated files
*.min.js
*.min.css

# Config files that should maintain their format
.husky/
.github/

# Assets
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.ico
*.webp

# Documentation that might have specific formatting
CHANGELOG.md
LICENSE

# IDE files
.vscode/
.idea/

# Temporary files
*.tmp
*.temp

# Logs
*.log

# Environment files
.env*

# Task master files
.taskmaster/
tasks/