import React from "react";
import { Box, Button } from "@mui/material";

interface ActionButton {
  label: string;
  onClick: () => void;
  variant?: "text" | "outlined" | "contained";
  color?:
    | "inherit"
    | "primary"
    | "secondary"
    | "success"
    | "error"
    | "info"
    | "warning";
  disabled?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

interface GenericActionButtonsProps {
  buttons: ActionButton[];
  justifyContent?:
    | "flex-start"
    | "center"
    | "flex-end"
    | "space-between"
    | "space-around"
    | "space-evenly";
  gap?: number;
  marginTop?: number;
  fullWidth?: boolean;
}

const GenericActionButtons: React.FC<GenericActionButtonsProps> = ({
  buttons,
  justifyContent = "flex-end",
  gap = 2,
  marginTop = 3,
  fullWidth = false,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        justifyContent,
        gap,
        mt: marginTop,
        width: fullWidth ? "100%" : "auto",
      }}
    >
      {buttons.map((button, index) => (
        <Button
          key={index}
          onClick={button.onClick}
          variant={button.variant || "text"}
          color={button.color || "primary"}
          disabled={button.disabled || false}
          startIcon={button.startIcon}
          endIcon={button.endIcon}
          fullWidth={fullWidth}
        >
          {button.label}
        </Button>
      ))}
    </Box>
  );
};

export default GenericActionButtons;
