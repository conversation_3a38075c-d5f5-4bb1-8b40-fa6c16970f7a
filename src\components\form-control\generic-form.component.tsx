import React from "react";
import {
  FormControl,
  InputLabel,
  OutlinedInput,
  FormHelperText,
} from "@mui/material";
import { GenericFormControlProps } from "@/interfaces/generic-form.interface";

const GenericFormControl: React.FC<GenericFormControlProps> = ({
  id,
  label,
  type,
  endAdornment,
  helperText,
  onChangeFn,
  error,
  placeholder,
  value,
}) => {
  return (
    <FormControl fullWidth error={error} margin="normal">
      <InputLabel
        htmlFor={id}
        sx={{
          color: "gray",
          "&.Mui-focused": {
            color: "gray",
          },
        }}
      >
        {label}
      </InputLabel>
      <OutlinedInput
        id={id}
        type={type}
        label={label}
        endAdornment={endAdornment}
        placeholder={placeholder}
        value={value}
        onChange={onChangeFn}
      ></OutlinedInput>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default GenericFormControl;
