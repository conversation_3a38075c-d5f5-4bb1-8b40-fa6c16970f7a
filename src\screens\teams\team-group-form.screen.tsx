import React from "react";
import { But<PERSON>, Box } from "@mui/material";
import { useFormikContext } from "formik";
import GenericFormControl from "@/components/form-control/generic-form.component";
import { IGroupFrom, IUserForm } from "@/interfaces/teams.interface";

const TeamGroupForm: React.FC<{
  openEdit?: boolean;
  currentUser?: IUserForm;
}> = () => {
  const { values, setFieldValue, errors, touched } = useFormikContext<IGroupFrom>();
  return (
    <>
      <GenericFormControl
        id="name"
        label="Group Name"
        type="text"
        value={values?.name}
        onChangeFn={e => setFieldValue("name", e.target.value)}
        error={touched.name && Boolean(errors.name)}
        helperText={touched.name && errors.name ? errors.name : undefined}
      />

      <Box mt={2}>
        <Button
          variant="contained"
          type="submit"
          fullWidth
          sx={{
            px: 2,
            py: 1.5,
            fontWeight: 600,
            fontSize: "1rem",
            textTransform: "none",
            borderRadius: 2,
            boxShadow: "0px 3px 6px rgba(0, 0, 0, 0.1)",
            backgroundColor: "custom.brand.primary",
            color: "white",
            "&:hover": {
              backgroundColor: "custom.brand.primary",
              filter: "brightness(0.95)",
            },
          }}
        >
          {values.name_display ? "Update Group" : "Save Group"}
        </Button>
      </Box>
    </>
  );
};

export default TeamGroupForm;
