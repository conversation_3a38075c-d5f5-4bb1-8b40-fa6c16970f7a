import { EProjectStatus } from "@/enums/projectStatus.enum";

export interface IAtomicNameId {
  id: string;
  name: string;
}

export interface ITimesheet {
  id: string;
  createdAt: string;
  updatedAt: string;
  user: IUser;
  project: IProject[];
  description: string;
  startTime: string;
  endTime: string;
  totalDuration: string;
  isBillable: string;
  isOt: string;
}

export interface IProject {
  name: string;
  id: string;
  colorHexValue: string;
  isPublic: boolean;
  isBillable: boolean;
  status: EProjectStatus;
  projectType: IAtomicNameId;
  clickupId: string;
  techStack: string;
}

export interface IUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  isActive: boolean;
  groups: IAtomicNameId[];
  permissions: string;
}
