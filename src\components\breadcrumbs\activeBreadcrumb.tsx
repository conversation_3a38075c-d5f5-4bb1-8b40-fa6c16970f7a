import {
  IActiveBreadcrumbProps,
  IBreadcrumbLinkProp,
} from "@/interfaces/activeBreadcrumbProps.interface";
import { Breadcrumbs } from "@mui/material";
import Link from "@mui/material/Link";

const ActiveBreadcrumb: React.FC<IActiveBreadcrumbProps> = ({
  color,
  links,
}) => {
  return (
    <div role="presentation">
      <Breadcrumbs color={color}>
        {links.map((link: IBreadcrumbLinkProp, index: number) => (
          <Link
            underline="hover"
            href={link.link}
            color={link.customColor ?? "inherit"}
            key={index}
          >
            {link.text}
          </Link>
        ))}
      </Breadcrumbs>
    </div>
  );
};

export default ActiveBreadcrumb;
