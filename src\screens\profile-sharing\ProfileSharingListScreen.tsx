// import React, { useState, useEffect } from "react";
// import {
//   Container,
//   Box,
//   Typography,
//   But<PERSON>,
//   I<PERSON><PERSON>utton,
//   Tooltip,
// } from "@mui/material";
// import { AddCircleOutline, FileCopy } from "@mui/icons-material";
// import { GridColDef } from "@mui/x-data-grid";
// import {
//   IProfileShare,
//   IProfileShareInput,
// } from "@/interfaces/profileSharing.interface";
// import ProfileSharingFormModal from "./ProfileSharingFormModal";
// import TableComponent from "@/components/table/table.component";
// import { TableAction } from "@/interfaces/table.interface";
// import ToastMessage from "@/components/toast/toast.component";

// const mockSharedProfiles: IProfileShare[] = [
//   {
//     id: "1",
//     profileId: "user123",
//     sharedWithEmail: "<EMAIL>",
//     accessLevel: "view",
//     expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//     createdAt: new Date().toISOString(),
//     updatedAt: new Date().toISOString(),
//     isActive: true,
//     shareLink: "http://localhost:5173/public-profile/user123",
//     notes: "Shared with client A for review.",
//   },
//   {
//     id: "2",
//     profileId: "user456",

//     accessLevel: "view",
//     createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
//     updatedAt: new Date().toISOString(),
//     isActive: true,
//     shareLink: "http://localhost:5173/public-profile/user456",
//   },
//   {
//     id: "3",
//     profileId: "user789",
//     sharedWithEmail: "<EMAIL>",
//     accessLevel: "view",
//     expiresAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
//     createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
//     updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
//     isActive: false,
//     shareLink: "http://localhost:5173/public-profile/user789",
//     notes: "Link expired.",
//   },
// ];

// const ProfileSharingListScreen: React.FC = () => {
//   const [sharedProfiles, setSharedProfiles] = useState<IProfileShare[]>([]);
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const [editingProfileShare, setEditingProfileShare] =
//     useState<IProfileShare | null>(null);

//   useEffect(() => {
//     setSharedProfiles(mockSharedProfiles);
//   }, []);

//   const handleOpenModal = (profileShare?: IProfileShare) => {
//     setEditingProfileShare(profileShare || null);
//     setIsModalOpen(true);
//   };

//   const handleCloseModal = () => {
//     setIsModalOpen(false);
//     setEditingProfileShare(null);
//   };

//   const handleSaveProfileShare = (data: IProfileShareInput | IProfileShare) => {
//     const newId = String(Date.now());
//     const profileIdToUse =
//       data.profileId || editingProfileShare?.profileId || "defaultProfile";

//     if (editingProfileShare) {
//       const updatedShare: IProfileShare = {
//         ...editingProfileShare,
//         ...data,
//         accessLevel: "view",
//         shareLink: `http://localhost:5173/public-profile/${profileIdToUse}`,
//         updatedAt: new Date().toISOString(),
//       };
//       setSharedProfiles((prev) =>
//         prev.map((ps) => (ps.id === updatedShare.id ? updatedShare : ps))
//       );
//     } else {
//       const newShare: IProfileShare = {
//         ...(data as IProfileShareInput),
//         id: newId,
//         accessLevel: "view",
//         shareLink: `http://localhost:5173/public-profile/${profileIdToUse}`,
//         createdAt: new Date().toISOString(),
//         updatedAt: new Date().toISOString(),
//       };
//       setSharedProfiles((prev) => [...prev, newShare]);
//     }
//     handleCloseModal();
//   };

//   const handleDeleteProfileShare = (id: string) => {
//     setSharedProfiles((prev) => prev.filter((ps) => ps.id !== id));
//   };

//   const handleCopyToClipboard = (link: string) => {
//     navigator.clipboard
//       .writeText(link)
//       .then(() => {
//         ToastMessage.success("Link copied to clipboard!");
//       })
//       .catch(() => {
//         ToastMessage.error("Failed to copy link");
//       });
//   };

//   const columns: GridColDef[] = [
//     {
//       field: "sharedWith",
//       headerName: "Shared With",
//       width: 200,
//       valueGetter: (_, row) => row.sharedWithEmail || "Anyone with link",
//     },
//     {
//       field: "status",
//       headerName: "Status",
//       width: 120,
//       valueGetter: (_, row) => (row.isActive ? "Active" : "Inactive"),
//     },
//     {
//       field: "expiresAt",
//       headerName: "Expires At",
//       width: 150,
//       valueGetter: (_, row) =>
//         row.expiresAt ? new Date(row.expiresAt).toLocaleDateString() : "Never",
//     },
//     {
//       field: "createdAt",
//       headerName: "Created At",
//       width: 150,
//       valueGetter: (_, row) => new Date(row.createdAt).toLocaleDateString(),
//     },
//     {
//       field: "shareLink",
//       headerName: "Share Link",
//       width: 250,
//       renderCell: (params) => (
//         <Box sx={{ display: "flex", alignItems: "center" }}>
//           <Tooltip title="Copy link">
//             <IconButton
//               size="small"
//               onClick={() => handleCopyToClipboard(params.row.shareLink)}
//             >
//               <FileCopy fontSize="small" />
//             </IconButton>
//           </Tooltip>
//           <Typography
//             variant="caption"
//             sx={{ ml: 1, display: { xs: "none", md: "inline" } }}
//           >
//             {params.row.shareLink.substring(0, 30)}...
//           </Typography>
//         </Box>
//       ),
//     },
//   ];

//   const actions: TableAction[] = [
//     {
//       label: "Edit",
//       handler: (row) => handleOpenModal(row as IProfileShare),
//     },
//     {
//       label: "Delete",
//       handler: (row) => handleDeleteProfileShare(row.id),
//     },
//   ];

//   return (
//     <Container
//       maxWidth="lg"
//       sx={{
//         py: 4,
//         mt: 8,
//       }}
//     >
//       <Box
//         sx={{
//           display: "flex",
//           justifyContent: "space-between",
//           alignItems: "center",
//           mb: 3,
//           position: "relative",
//           zIndex: 1,
//         }}
//       >
//         <Typography variant="h4" component="h1" gutterBottom>
//           Manage Shared Profiles
//         </Typography>
//         <Button
//           variant="contained"
//           startIcon={<AddCircleOutline />}
//           onClick={() => handleOpenModal()}
//         >
//           Create New Share Link
//         </Button>
//       </Box>

//       <TableComponent
//         columns={columns}
//         rows={sharedProfiles}
//         loading={false}
//         actions={actions}
//         hideFooter={false}
//         contentAlign="left"
//         rowCount={sharedProfiles.length}
//       />

//       <ProfileSharingFormModal
//         open={isModalOpen}
//         onClose={handleCloseModal}
//         onSave={handleSaveProfileShare}
//         profileShare={editingProfileShare}
//       />
//     </Container>
//   );
// };

// export default ProfileSharingListScreen;
