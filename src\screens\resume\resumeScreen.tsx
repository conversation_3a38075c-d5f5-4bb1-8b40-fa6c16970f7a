import { useParams, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { Box, Typography, Paper, Button, Divider, Avatar, Chip, Grid, useTheme, useMediaQuery } from "@mui/material";
import ToastMessage from "@/components/toast/toast.component";
import EmployeeProfileService from "@/core/api/employee-profile.service";
import UsersService from "@/core/api/users.service";
import { IEmployeeProfile } from "@/interfaces/employeeProfile.interface";

const labelStyle = { fontWeight: 600, fontSize: 13, color: "#888" };
const valueStyle = { fontWeight: 500, fontSize: 15 };

const ResumeScreen = () => {
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const [profile, setProfile] = useState<IEmployeeProfile | null>(null);
  const [loading, setLoading] = useState(true);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      try {
        const usersResource = new UsersService.UsersResource();
        let userResponse, resumeResponse, userId;
        if (id) {
          userResponse = await usersResource.get(id);
          resumeResponse = await usersResource.getUserResume(id);
          userId = id;
        } else {
          userResponse = await usersResource.get("my-profile");
          userId = userResponse.data.id;
          resumeResponse = await usersResource.getUserResume(userId);
        }

        const converted = EmployeeProfileService.convertToProfile(resumeResponse.data, {
          id: userResponse.data.id,
          firstName: userResponse.data.first_name,
          lastName: userResponse.data.last_name,
          email: userResponse.data.email,
          department: userResponse.data.groups?.[0]?.name || "Unknown",
          jobTitle: "Team Member",
          contactNumber: "",
          address: "",
        });

        setProfile(converted);
      } catch {
        ToastMessage.error("Failed to load employee profile.");
        setProfile(null);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  if (loading) {
    return (
      <Box p={3}>
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  if (!profile) {
    return (
      <Box p={3}>
        <Typography color="error">Profile not found.</Typography>
      </Box>
    );
  }

  const { selectedEmployee, candidateBackground, languages, availability, education, workExperience } = profile;

  return (
    <Box p={0} m={0} width="100%" minHeight="100vh" sx={{ background: "#fff" }}>
      <Paper
        elevation={0}
        sx={{
          p: { xs: 1, sm: 2 },
          borderRadius: 0,

          minHeight: "100vh",
          width: "100%",
          boxSizing: "border-box",
          overflowX: "auto",
          position: "relative",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: 8,
            right: 16,
            zIndex: 1000,
          }}
        >
          <Button
            variant="contained"
            size="small"
            onClick={() => selectedEmployee?.id && navigate(`/settings/public-profile/edit/${selectedEmployee.id}`)}
          >
            Edit Profile
          </Button>
        </Box>

        {/* Avatar and Name */}
        <Grid container spacing={2} alignItems="center" wrap="wrap">
          <Grid size="auto">
            <Avatar
              sx={{
                width: isMobile ? 48 : 64,
                height: isMobile ? 48 : 64,
                bgcolor: "#bdbdbd",
                fontSize: isMobile ? 20 : 28,
              }}
            >
              {selectedEmployee.firstName?.[0]?.toUpperCase()}
              {selectedEmployee.lastName?.[0]?.toUpperCase()}
            </Avatar>
          </Grid>
          <Grid size="grow" sx={{ minWidth: 0 }}>
            <Typography variant={isMobile ? "h6" : "h5"} fontWeight={700} noWrap>
              {selectedEmployee.firstName} {selectedEmployee.lastName}
            </Typography>
            <Typography color="text.secondary" fontWeight={500}>
              {selectedEmployee.jobTitle}
            </Typography>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        {/* Info grid */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid size={{ xs: 12, sm: 6 }}>
            <Typography sx={labelStyle}>Email</Typography>
            <Typography sx={valueStyle} noWrap={isMobile}>
              {selectedEmployee.email}
            </Typography>
          </Grid>
          <Grid size={{ xs: 12, sm: 6 }}>
            <Typography sx={labelStyle}>Department</Typography>
            <Typography sx={valueStyle}>{selectedEmployee.department}</Typography>
          </Grid>
          <Grid size={{ xs: 12, sm: 6 }}>
            <Typography sx={labelStyle}>Contact Number</Typography>
            <Typography sx={valueStyle}>{selectedEmployee.contactNumber || ""}</Typography>
          </Grid>
          <Grid size={{ xs: 12, sm: 6 }}>
            <Typography sx={labelStyle}>Address</Typography>
            <Typography sx={valueStyle}>{selectedEmployee.address || ""}</Typography>
          </Grid>
        </Grid>

        {/* Candidate background */}
        <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
          Candidate Background
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6 }}>
            <Typography sx={labelStyle}>Core Languages</Typography>
            {candidateBackground?.coreLanguages?.length ? (
              candidateBackground.coreLanguages.map(lang => <Chip key={lang} label={lang} sx={{ mr: 1, mb: 1 }} />)
            ) : (
              <Typography color="text.secondary"></Typography>
            )}
          </Grid>
          <Grid size={{ xs: 12, sm: 6 }}>
            <Typography sx={labelStyle}>Core Frameworks</Typography>
            {candidateBackground?.coreFrameworks?.length ? (
              candidateBackground.coreFrameworks.map(fw => <Chip key={fw} label={fw} sx={{ mr: 1, mb: 1 }} />)
            ) : (
              <Typography color="text.secondary"></Typography>
            )}
          </Grid>
          <Grid size={12}>
            <Typography sx={labelStyle}>Additional Skills</Typography>
            {candidateBackground?.additionalSkills?.length ? (
              candidateBackground.additionalSkills.map(skill => (
                <Chip key={skill} label={skill} sx={{ mr: 1, mb: 1 }} />
              ))
            ) : (
              <Typography color="text.secondary"></Typography>
            )}
          </Grid>
        </Grid>

        {/* Languages */}
        <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
          Languages
        </Typography>
        <Divider sx={{ mb: 2 }} />
        {languages?.length ? (
          languages.map((lang, idx) => (
            <Box key={idx} mb={1}>
              <Typography sx={labelStyle}>{lang.language}</Typography>
              <Typography sx={valueStyle}>{lang.proficiency}</Typography>
            </Box>
          ))
        ) : (
          <Typography color="text.secondary"></Typography>
        )}

        {/* Availability */}
        <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
          Availability
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Typography sx={labelStyle}>Availability</Typography>
        <Typography sx={valueStyle}>{availability || ""}</Typography>

        {/* Education */}
        <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
          Education
        </Typography>
        <Divider sx={{ mb: 2 }} />
        {education?.length ? (
          education.map((edu, idx) => (
            <Box key={idx} mb={1}>
              <Typography sx={labelStyle}>{edu.degree}</Typography>
              <Typography sx={valueStyle}>{edu.institution}</Typography>
              <Typography sx={valueStyle}>{edu.graduationYear}</Typography>
            </Box>
          ))
        ) : (
          <Typography color="text.secondary"></Typography>
        )}

        {/* Work Experience */}
        <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
          Work Experience
        </Typography>
        <Divider sx={{ mb: 2 }} />
        {workExperience?.length ? (
          workExperience.map((exp, idx) => (
            <Box key={idx} mb={1}>
              <Typography sx={labelStyle}>{exp.role}</Typography>
              <Typography sx={valueStyle}>{exp.company}</Typography>
              <Typography sx={valueStyle}>
                {exp.startDate} - {exp.endDate || "Present"}
              </Typography>
            </Box>
          ))
        ) : (
          <Typography color="text.secondary"></Typography>
        )}
      </Paper>
    </Box>
  );
};

export default ResumeScreen;
