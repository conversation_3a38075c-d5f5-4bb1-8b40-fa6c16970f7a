import React, { useState } from "react";
import { useTheme } from "@mui/material/styles";
import LoginWrapper from "@/components/login-wrapper/login-wrapper.component";
import {
  Box,
  Card,
  CardContent,
  Typography,
  But<PERSON>,
  Link,
} from "@mui/material";
import GenericFormControl from "@/components/form-control/generic-form.component";
import ToastMessage from "@/components/toast/toast.component";
import { EMAIL_REGEX } from "@/utils/regex";
import AuthResource from "@/core/api/auth";

const ForgotPassword: React.FC = () => {
  const auth = new AuthResource();
  const [email, setEmail] = useState<string>("");
  const theme = useTheme();

  const handleResetPassword = async () => {
    if (!email) {
      ToastMessage.error("Email field is empty");
      return;
    }
    const isEmailValid = EMAIL_REGEX.test(email);
    if (!isEmailValid) {
      ToastMessage.info("Email format is not valid");
      return;
    }
    try {
      const response = await auth.forgetPassword(email);

      switch (response.status) {
        case 200:
          setEmail("");
          ToastMessage.success(
            "Password reset link sent! Please check your email.",
          );
          return;
        case 404:
          ToastMessage.error("Specified email could not be found");
          return;
        default:
          ToastMessage.error("Something went wrong");
          return;
      }
    } catch {
      ToastMessage.error("Something went wrong");
    }
  };

  const forgorPasswordContainer = (
    <Box>
      <Card>
        <CardContent>
          <Typography variant="h1" gutterBottom fontWeight={500}>
            Forgot Password?
          </Typography>
          <p>Please enter the email you use to sign in to your account.</p>
          <GenericFormControl
            id="forgot-password-field"
            label="Your Email Address"
            type="text"
            value={email}
            onChangeFn={(e) => setEmail(e.target.value)}
          ></GenericFormControl>
          <Button
            fullWidth
            variant="contained"
            sx={{
              bgcolor: theme.palette.custom.brand.azureRadiance,
              textTransform: "none",
              fontWeight: 600,
              py: 1,
            }}
            type="submit"
            onClick={handleResetPassword}
          >
            Request Password Reset
          </Button>
          <Box sx={{ textAlign: "center", marginTop: 2 }}>
            <Link href="/login" underline="none" fontSize="0.85rem">
              Back to Login
            </Link>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );

  return <LoginWrapper children={forgorPasswordContainer}></LoginWrapper>;
};

export default ForgotPassword;
