import { useState, useEffect } from "react";
import { formatTime } from "@/utils/time";

export const useElapsedTimer = (isTracking: boolean, startTime?: string) => {
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    const calculateElapsedTime = () => {
      if (isTracking && startTime) {
        const start = new Date(startTime);
        const now = new Date();
        const elapsed = Math.floor((now.getTime() - start.getTime()) / 1000);
        setElapsedTime(Math.max(0, elapsed));
      } else {
        setElapsedTime(0);
      }
    };

    calculateElapsedTime();

    if (isTracking) {
      interval = setInterval(calculateElapsedTime, 1000);
    }

    return () => clearInterval(interval);
  }, [isTracking, startTime]);

  return {
    elapsedTime,
    formattedTime: formatTime(elapsedTime),
    setElapsedTime,
  };
};
