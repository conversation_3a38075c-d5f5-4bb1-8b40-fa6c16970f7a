import * as Yup from "yup";
import { DetailNode } from "./employee-profile-form-utils";

/**
 * Enhanced validation schema for Employee Profile Form
 * Provides comprehensive validation for all sections and fields
 */

// Constants for validation
const CURRENT_YEAR = new Date().getFullYear();
const MIN_GRADUATION_YEAR = 1950;
const MAX_GRADUATION_YEAR = CURRENT_YEAR + 6; // Allow up to 6 years in future for ongoing studies

// Simplified validation schema that can be extended with custom validation functions
export const employeeProfileValidationSchema = Yup.object({
  user_id: Yup.string()
    .required("Employee selection is required")
    .min(1, "Please select an employee"),

  details: Yup.array()
    .of(
      Yup.object({
        value: Yup.string().required(),
        children: Yup.array().optional(),
      }),
    )
    .required("Profile sections are required"),
});

// Specific validation functions for each section
export const validateWorkExperienceSection = (
  workExperience: DetailNode[],
): string[] => {
  const errors: string[] = [];

  workExperience.forEach((work, index) => {
    if (!work.value || work.value.trim().length < 2) {
      errors.push(
        `Work Experience ${index + 1}: Company name is required and must be at least 2 characters`,
      );
    }

    if (work.value && work.value.length > 100) {
      errors.push(
        `Work Experience ${index + 1}: Company name must be less than 100 characters`,
      );
    }

    const roleChild = work.children?.find(
      (c: DetailNode) => c.value === "role",
    );
    const role = roleChild?.children?.[0]?.value;
    if (!role || role.trim().length < 2) {
      errors.push(
        `Work Experience ${index + 1}: Role is required and must be at least 2 characters`,
      );
    }

    const durationChild = work.children?.find(
      (c: DetailNode) => c.value === "duration",
    );
    const duration = durationChild?.children?.[0]?.value;
    if (!duration) {
      errors.push(`Work Experience ${index + 1}: Duration is required`);
    } else {
      // Validate duration format and logic
      const parts = duration.split(" - ");
      if (parts.length !== 2) {
        errors.push(`Work Experience ${index + 1}: Invalid duration format`);
      } else {
        const startDate = parts[0]?.trim();
        const endDate = parts[1]?.trim();
        const isCurrent = endDate === "Present";

        if (!startDate || !/^\d{4}-\d{2}$/.test(startDate)) {
          errors.push(
            `Work Experience ${index + 1}: Invalid start date format (use YYYY-MM)`,
          );
        }

        if (!isCurrent && (!endDate || !/^\d{4}-\d{2}$/.test(endDate))) {
          errors.push(
            `Work Experience ${index + 1}: Invalid end date format (use YYYY-MM)`,
          );
        }

        // Check if dates are not in the future
        const today = new Date();
        const start = new Date(startDate + "-01");
        if (start > today) {
          errors.push(
            `Work Experience ${index + 1}: Start date cannot be in the future`,
          );
        }

        if (!isCurrent) {
          const end = new Date(endDate + "-01");
          if (end > today) {
            errors.push(
              `Work Experience ${index + 1}: End date cannot be in the future`,
            );
          }
          if (end < start) {
            errors.push(
              `Work Experience ${index + 1}: End date must be after start date`,
            );
          }
        }
      }
    }

    const descriptionChild = work.children?.find(
      (c: DetailNode) => c.value === "description",
    );
    const description = descriptionChild?.children?.[0]?.value;
    if (!description || description.trim().length < 10) {
      errors.push(
        `Work Experience ${index + 1}: Description is required and must be at least 10 characters`,
      );
    }

    if (description && description.length > 2000) {
      errors.push(
        `Work Experience ${index + 1}: Description must be less than 2000 characters`,
      );
    }
  });

  return errors;
};

export const validateEducationSection = (education: DetailNode[]): string[] => {
  const errors: string[] = [];

  education.forEach((edu, index) => {
    if (!edu.value || edu.value.trim().length < 2) {
      errors.push(
        `Education ${index + 1}: Institution name is required and must be at least 2 characters`,
      );
    }

    if (edu.value && edu.value.length > 100) {
      errors.push(
        `Education ${index + 1}: Institution name must be less than 100 characters`,
      );
    }

    const degree = edu.children?.[0]?.value;
    if (!degree || degree.trim().length < 2) {
      errors.push(
        `Education ${index + 1}: Degree is required and must be at least 2 characters`,
      );
    }

    if (degree && degree.length > 100) {
      errors.push(
        `Education ${index + 1}: Degree must be less than 100 characters`,
      );
    }

    // Optional graduation year validation
    const graduationYear = edu.children?.[2]?.value;
    if (graduationYear) {
      const year = parseInt(graduationYear);
      if (
        isNaN(year) ||
        year < MIN_GRADUATION_YEAR ||
        year > MAX_GRADUATION_YEAR
      ) {
        errors.push(
          `Education ${index + 1}: Graduation year must be between ${MIN_GRADUATION_YEAR} and ${MAX_GRADUATION_YEAR}`,
        );
      }
    }
  });

  return errors;
};

export const validateLanguagesSection = (languages: DetailNode[]): string[] => {
  const errors: string[] = [];
  const validLanguages = [
    "English",
    "Spanish",
    "French",
    "German",
    "Italian",
    "Portuguese",
    "Dutch",
    "Russian",
    "Chinese (Mandarin)",
    "Chinese (Cantonese)",
    "Japanese",
    "Korean",
    "Arabic",
    "Hindi",
    "Bengali",
    "Punjabi",
    "Tamil",
    "Telugu",
    "Marathi",
    "Gujarati",
    "Urdu",
    "Turkish",
    "Polish",
    "Romanian",
    "Czech",
    "Hungarian",
    "Swedish",
    "Norwegian",
    "Danish",
    "Finnish",
    "Greek",
    "Hebrew",
    "Thai",
    "Vietnamese",
    "Indonesian",
    "Malay",
    "Tagalog",
    "Swahili",
  ];
  const validProficiencies = ["Beginner", "Intermediate", "Advanced", "Native"];

  languages.forEach((lang, index) => {
    if (!lang.value || !validLanguages.includes(lang.value)) {
      errors.push(`Language ${index + 1}: Please select a valid language`);
    }

    const proficiency = lang.children?.[0]?.value;
    if (!proficiency || !validProficiencies.includes(proficiency)) {
      errors.push(
        `Language ${index + 1}: Please select a valid proficiency level`,
      );
    }
  });

  return errors;
};

export const validateBackgroundSection = (
  backgroundChildren: DetailNode[],
): string[] => {
  const errors: string[] = [];

  const coreLanguageGroup = backgroundChildren.find(
    (group) => group.value === "core language",
  );
  if (!coreLanguageGroup?.children || coreLanguageGroup.children.length === 0) {
    errors.push("At least one core programming language is required");
  } else {
    coreLanguageGroup.children.forEach((skill: DetailNode, index: number) => {
      if (!skill.value || skill.value.trim().length === 0) {
        errors.push(
          `Core Language ${index + 1}: Programming language cannot be empty`,
        );
      }
    });
  }

  // Validate other skill groups
  backgroundChildren.forEach((group) => {
    if (
      group.value === "core framework" ||
      group.value === "additional skills"
    ) {
      group.children?.forEach((skill: DetailNode, index: number) => {
        if (skill.value && skill.value.length > 50) {
          errors.push(
            `${group.value} ${index + 1}: Skill name must be less than 50 characters`,
          );
        }
      });
    }
  });

  return errors;
};

export const validateAvailabilitySection = (
  availability: DetailNode[],
): string[] => {
  const errors: string[] = [];
  const validOptions = [
    "Available immediately",
    "Available in 2 weeks",
    "Available in 1 month",
    "Not currently available",
  ];

  if (availability.length === 0) {
    errors.push("Availability must be specified");
  } else {
    const selectedAvailability = availability[0]?.value;
    if (!selectedAvailability || !validOptions.includes(selectedAvailability)) {
      errors.push("Please select a valid availability option");
    }
  }

  return errors;
};

// Custom validation for duplicate detection
export const validateNoDuplicates = (values: {
  details: DetailNode[];
}): string[] => {
  const errors: string[] = [];

  // Check for duplicate languages
  const languagesSection = values.details.find((d) => d.value === "Languages");
  if (languagesSection?.children) {
    const languages = languagesSection.children.map((l) =>
      l.value.toLowerCase(),
    );
    const duplicateLanguages = languages.filter(
      (lang, index) => languages.indexOf(lang) !== index,
    );
    if (duplicateLanguages.length > 0) {
      errors.push("Duplicate languages are not allowed");
    }
  }

  // Check for duplicate companies in work experience
  const workSection = values.details.find((d) => d.value === "Work Experience");
  if (workSection?.children) {
    const companies = workSection.children.map((w) =>
      w.value.toLowerCase().trim(),
    );
    const duplicateCompanies = companies.filter(
      (company, index) => companies.indexOf(company) !== index,
    );
    if (duplicateCompanies.length > 0) {
      errors.push("Duplicate companies in work experience are not recommended");
    }
  }

  // Check for duplicate institutions in education
  const educationSection = values.details.find((d) => d.value === "Education");
  if (educationSection?.children) {
    const institutions = educationSection.children.map((e) =>
      e.value.toLowerCase().trim(),
    );
    const duplicateInstitutions = institutions.filter(
      (inst, index) => institutions.indexOf(inst) !== index,
    );
    if (duplicateInstitutions.length > 0) {
      errors.push("Duplicate institutions in education are not recommended");
    }
  }

  return errors;
};

// Helper function to validate overlapping work experience dates
export const validateWorkExperienceOverlap = (values: {
  details: DetailNode[];
}): string[] => {
  const errors: string[] = [];
  const workSection = values.details.find((d) => d.value === "Work Experience");

  if (!workSection?.children || workSection.children.length < 2) return errors;

  const workPeriods = workSection.children
    .map((work, index) => {
      const durationChild = work.children?.find((c) => c.value === "duration");
      const durationString = durationChild?.children?.[0]?.value || "";

      if (!durationString) return null;

      const parts = durationString.split(" - ");
      if (parts.length !== 2) return null;

      const startDate = new Date(parts[0] + "-01");
      const endDate =
        parts[1] === "Present" ? new Date() : new Date(parts[1] + "-01");

      return { index, startDate, endDate, company: work.value };
    })
    .filter(Boolean);

  // Check for overlapping periods (only warn, don't error)
  for (let i = 0; i < workPeriods.length; i++) {
    for (let j = i + 1; j < workPeriods.length; j++) {
      const period1 = workPeriods[i];
      const period2 = workPeriods[j];

      if (!period1 || !period2) continue;

      const overlap =
        period1.startDate < period2.endDate &&
        period2.startDate < period1.endDate;
      if (overlap) {
        errors.push(
          `Overlapping work periods detected between ${period1.company} and ${period2.company}. Please verify the dates are correct.`,
        );
      }
    }
  }

  return errors;
};

export default employeeProfileValidationSchema;
