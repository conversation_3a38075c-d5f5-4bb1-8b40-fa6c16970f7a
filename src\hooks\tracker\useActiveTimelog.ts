import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { TimelogData } from "@/types/tracker";

export const useActiveTimelog = (userId: string | undefined) => {
  const timelogResource = useMemo(() => new Resource("timelogs/weekly"), []);

  return useQuery({
    queryKey: ["timelogs", userId, "weekly"],
    queryFn: () =>
      timelogResource.list({
        user: userId,
      }),
    enabled: !!userId,
    select: data => {
      // Get the first week's logs (most recent week)
      const firstWeek = data?.data?.results?.[0];
      if (!firstWeek?.logs || firstWeek.logs.length === 0) {
        return null;
      }

      // Get the first log in the most recent week (logs are already sorted in descending order)
      const firstTimelog = firstWeek.logs[0] as TimelogData | undefined;

      // If first item has no end_time or null, it's the active timelog
      return firstTimelog?.end_time == null ? firstTimelog : null;
    },
    placeholderData: keepPreviousData,
    staleTime: 0,
    refetchInterval: 10000, // Refetch every 10 seconds to balance performance and synchronization
    refetchOnWindowFocus: true,
  });
};
