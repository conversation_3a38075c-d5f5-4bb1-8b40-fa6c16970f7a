import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useMemo } from "react";
import { format } from "date-fns";
import Resource from "@/core/api/resource";
import { ProjectSummary } from "@/types/reports";

interface UseWeeklySummaryReportParams {
  dateRange: { start: Date; end: Date };
  selectedProjects: string[];
  selectedUsers: string[];
  billableFilter: boolean;
  otFilter: boolean;
  enabled?: boolean;
}

interface WeeklySummaryResponse {
  data: ProjectSummary[];
}

export const useWeeklySummaryReport = ({
  dateRange,
  selectedProjects,
  selectedUsers,
  billableFilter,
  otFilter,
  enabled = true,
}: UseWeeklySummaryReportParams) => {
  const reportResource = useMemo(() => new Resource("timelogs/report/summary"), []);

  const queryKey = [
    "weekly-summary-report",
    format(dateRange.start, "yyyy-MM-dd"),
    format(dateRange.end, "yyyy-MM-dd"),
    [...selectedProjects].sort().join(","),
    [...selectedUsers].sort().join(","),
    billableFilter,
    otFilter,
  ];

  const queryFn = async (): Promise<ProjectSummary[]> => {
    const params = {
      start_time__gte: format(dateRange.start, "yyyy-MM-dd"),
      end_time__lte: format(dateRange.end, "yyyy-MM-dd"),
      ...(selectedProjects.length > 0 && { project__in: selectedProjects.join(",") }),
      ...(selectedUsers.length > 0 && { user__in: selectedUsers.join(",") }),
      ...(billableFilter && { is_billable: true }),
      ...(otFilter && { is_ot: true }),
    };

    const response: WeeklySummaryResponse = await reportResource.list(params);
    return response.data;
  };

  return useQuery({
    queryKey,
    queryFn,
    enabled,
    placeholderData: keepPreviousData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
