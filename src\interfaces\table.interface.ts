import { GridFeatureMode } from "@/enums/enum";
import { GridColDef, GridFilterModel, GridPaginationModel, GridSortModel, GridValidRowModel } from "@mui/x-data-grid";

export interface TableColumn {
  field: string;
  headerName: string;
  width?: number;
  type?: string;
  editable?: boolean;
}

export interface TableAction {
  label: string;
  handler: (row: GridValidRowModel) => void;
  shouldShow?: (row: GridValidRowModel) => boolean;
}

export type TableProps = {
  columns: GridColDef<GridValidRowModel[number]>[];
  rows: GridValidRowModel[];
  pageSize?: number;
  loading: boolean;
  actions?: TableAction[];
  hideFooter?: boolean;
  contentAlign?: "left" | "center" | "right";
  onSortChange?: (model: GridSortModel) => void;
  onFilterChange?: (model: GridFilterModel) => void;
  onPaginationChange?: (model: GridPaginationModel) => void;
  rowCount: number;
  paginationMode?: GridFeatureMode;
  sortingMode?: GridFeatureMode;
};

export interface ErrorResponse {
  response?: {
    data?: {
      error?: string | string[]; // can be a string or array of strings
    };
    status?: number;
  };
  message?: string;
}
