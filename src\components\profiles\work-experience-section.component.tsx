import React from "react";
import {
  Text<PERSON>ield,
  Grid,
  IconButton,
  Box,
  Autocomplete,
  Chip,
  Checkbox,
  FormControlLabel,
  Button,
} from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import RichTextEditor from "@/components/form-control/rich-text-editor.component";
import { parseDurationString, formatDurationString } from "@/utils/work-experience-utils";
import { DetailNode, createNewWorkExperience } from "@/utils/employee-profile-form-utils";
import { allSkills } from "./constants";
import { validateSingleField } from "@/utils/field-validation-helpers";

interface WorkExperienceSectionProps {
  sectionIndex: number;
  section: DetailNode;
  setFieldValue: (field: string, value: unknown) => void;
}

const WorkExperienceSection: React.FC<WorkExperienceSectionProps> = ({ sectionIndex, section, setFieldValue }) => {
  const addWorkExperience = () => {
    const currentWork = section.children || [];
    const newWork = [...currentWork, createNewWorkExperience()];
    setFieldValue(`details[${sectionIndex}].children`, newWork);
  };

  const removeWorkExperience = (index: number) => {
    const currentWork = section.children || [];
    const newWork = currentWork.filter((_, i) => i !== index);
    setFieldValue(`details[${sectionIndex}].children`, newWork);
  };

  const updateWorkField = (workIndex: number, field: string, value: string | string[]) => {
    const currentWork = section.children || [];
    const newWork = [...currentWork];
    const work = newWork[workIndex];

    if (!work) return;
    if (field === "company") {
      work.value = value as string;
    } else {
      const fieldIndex = work.children?.findIndex(child => child.value?.toLowerCase() === field.toLowerCase()) ?? -1;

      if (fieldIndex >= 0 && work.children && work.children[fieldIndex]) {
        if (field.toLowerCase() === "technologies") {
          work.children[fieldIndex].children = (value as string[]).map(tech => ({ value: tech }));
        } else {
          work.children[fieldIndex].children = [{ value: value as string }];
        }
      } else if (field.toLowerCase() === "technologies") {
        // Create technologies node if it doesn't exist
        if (!work.children) {
          work.children = [];
        }
        work.children.push({
          value: "technologies",
          children: (value as string[]).map(tech => ({ value: tech })),
        });
      }
    }

    setFieldValue(`details[${sectionIndex}].children`, newWork);
  };

  const updateDurationField = (workIndex: number, startDate: string, endDate: string, isCurrent: boolean) => {
    const durationString = formatDurationString(startDate, endDate, isCurrent);
    updateWorkField(workIndex, "duration", durationString);
  };

  return (
    <Box>
      {section.children?.map((work, index) => {
        const durationString = work.children?.find(c => c.value === "duration")?.children?.[0]?.value || "";
        const { startDate, endDate, isCurrent } = parseDurationString(durationString);
        return (
          <Box key={work.id ?? index} sx={{ mb: 3, p: 2, border: "1px solid #e0e0e0", borderRadius: 1 }}>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Company"
                  value={work.value || ""}
                  onChange={e => updateWorkField(index, "company", e.target.value)}
                  error={!!validateSingleField("company", work.value || "")}
                  helperText={validateSingleField("company", work.value || "")}
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Role"
                  value={work.children?.find(c => c.value === "role")?.children?.[0]?.value || ""}
                  onChange={e => updateWorkField(index, "role", e.target.value)}
                  error={
                    !!validateSingleField(
                      "role",
                      work.children?.find(c => c.value === "role")?.children?.[0]?.value || ""
                    )
                  }
                  helperText={validateSingleField(
                    "role",
                    work.children?.find(c => c.value === "role")?.children?.[0]?.value || ""
                  )}
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="Start Date"
                  type="month"
                  InputLabelProps={{ shrink: true }}
                  value={startDate}
                  onChange={e => {
                    updateDurationField(index, e.target.value, endDate, isCurrent);
                  }}
                  error={!!validateSingleField("duration", durationString)}
                  helperText={validateSingleField("duration", durationString)}
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <TextField
                  fullWidth
                  label="End Date"
                  type="month"
                  InputLabelProps={{ shrink: true }}
                  value={endDate}
                  onChange={e => {
                    updateDurationField(index, startDate, e.target.value, isCurrent);
                  }}
                  disabled={isCurrent}
                  error={!!validateSingleField("duration", durationString)}
                  helperText={!isCurrent ? validateSingleField("duration", durationString) : undefined}
                />
              </Grid>
              <Grid size={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isCurrent}
                      onChange={e => {
                        updateDurationField(index, startDate, endDate, e.target.checked);
                      }}
                    />
                  }
                  label="Currently working here"
                />
              </Grid>
              <Grid size={12}>
                <RichTextEditor
                  label="Description/Responsibilities"
                  value={work.children?.find(c => c.value === "description")?.children?.[0]?.value || ""}
                  onChange={content => updateWorkField(index, "description", content)}
                  height={200}
                />
              </Grid>
              <Grid size={12}>
                <Autocomplete
                  multiple
                  options={allSkills}
                  freeSolo
                  value={
                    work.children?.find(c => c.value === "technologies")?.children?.map(child => child.value) || []
                  }
                  onChange={(_, newValue) => updateWorkField(index, "technologies", newValue)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, tagIndex) => (
                      <Chip variant="outlined" label={option} {...getTagProps({ index: tagIndex })} key={tagIndex} />
                    ))
                  }
                  renderInput={params => (
                    <TextField {...params} label="Technologies Used" placeholder="Add technologies..." />
                  )}
                />
              </Grid>
              <Grid size={12}>
                <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
                  <IconButton onClick={() => removeWorkExperience(index)} color="error">
                    <RemoveCircleOutlineIcon />
                  </IconButton>
                </Box>
              </Grid>
            </Grid>
          </Box>
        );
      })}
      <Button startIcon={<AddCircleOutlineIcon />} onClick={addWorkExperience} variant="outlined" size="small">
        Add Work Experience
      </Button>
    </Box>
  );
};

export default WorkExperienceSection;
