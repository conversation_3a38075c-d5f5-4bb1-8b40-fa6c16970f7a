import { ClientStatus } from "@/enums/enum";

export const clientStatusLabels: Record<ClientStatus, string> = {
  [ClientStatus.ACTIVE]: "Active",
  [ClientStatus.ARCHIVED]: "Archived",
};

export interface ClientFormData {
  name: string;
  description: string;
  image?: string | null;
  email: string | null;
  address: string | null;
  country: string | null;
  phone: string | null;
  status: string | null;
}
export interface Client {
  id: string;
  name: string;
  description: string;
  role: string;
  // image: string | null;
  email: string | null;
  address: string | null;
  country: string | null;
  phone: string | null;
  status: string | null;
}
export interface UpdateClientFormData extends Partial<ClientFormData> {
  id: string;
}
