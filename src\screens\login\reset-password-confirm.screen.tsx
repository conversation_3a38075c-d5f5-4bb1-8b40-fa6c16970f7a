import React, { useState, useEffect } from "react";
import { useTheme } from "@mui/material/styles";
import { useNavigate, useParams } from "react-router-dom";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Link,
  useMediaQuery,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";
import GenericFormControl from "@/components/form-control/generic-form.component";
import ToastMessage from "@/components/toast/toast.component";
import AuthResource from "@/core/api/auth";
import LoginWrapper from "@/components/login-wrapper/login-wrapper.component";
import { ErrorResponse } from "@/interfaces/profile.interface";

const ResetPasswordConfirm: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const auth = React.useMemo(() => new AuthResource(), []);
  const navigate = useNavigate();
  const { uid, token } = useParams<{ uid: string; token: string }>();

  const [showPassword, setShowPassword] = useState({
    new: false,
    confirm: false,
  });
  const [linkExpired, setLinkExpired] = useState(false);
  const [checking, setChecking] = useState(true);

  useEffect(() => {
    const checkToken = async () => {
      if (!uid || !token) {
        setLinkExpired(true);
        setChecking(false);
        return;
      }
      try {
        await auth.verifyResetToken({ uid, token });
        setLinkExpired(false);
      } catch {
        setLinkExpired(true);
      } finally {
        setChecking(false);
      }
    };
    checkToken();
  }, [uid, token, auth]);

  const formik = useFormik({
    initialValues: {
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      newPassword: Yup.string().min(6, "Password must be at least 6 characters").required("Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("newPassword")], "Passwords must match")
        .required("Confirm Password is required"),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      if (!uid || !token) {
        ToastMessage.error("Invalid or expired password reset link.");
        setLinkExpired(true);
        return;
      }
      setSubmitting(true);
      try {
        await auth.resetPasswordConfirm({
          uid,
          token,
          new_password: values.newPassword,
          confirm_password: values.confirmPassword,
        });
        ToastMessage.success("Password reset successful! Please log in.");
        navigate("/login");
      } catch (error: unknown) {
        const err = error as ErrorResponse;
        const data = err?.response?.data || {};

        const tokenError = data.token?.[0] || data.detail || data.error || "";

        if (
          tokenError.toLowerCase().includes("expired") ||
          tokenError.toLowerCase().includes("invalid") ||
          tokenError.toLowerCase().includes("not valid") ||
          tokenError.toLowerCase().includes("already been used")
        ) {
          setLinkExpired(true);
          return;
        }

        ToastMessage.error("Failed to reset password. Please try again.");
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleToggleShowPassword = (field: "new" | "confirm") => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  if (checking) {
    return <div>Loading...</div>;
  }

  if (linkExpired) {
    return (
      <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center" flexDirection="column" px={2}>
        <img src="/assets/svg/expired-svg.svg" alt="Link expired" style={{ width: 120, marginBottom: 16 }} />
        <Typography variant="h4" color="error" gutterBottom>
          Link Expired
        </Typography>
        <Typography variant="body1" sx={{ mb: 3, textAlign: "center" }}>
          This password reset link has already been used or is expired.
        </Typography>
        <Button
          variant="contained"
          href="/login"
          sx={{
            textTransform: "none",
            bgcolor: theme.palette.custom?.brand?.azureRadiance || theme.palette.primary.main,
            color: "white",
            "&:hover": {
              bgcolor: theme.palette.custom?.brand?.azureRadiance
                ? theme.palette.custom.brand.azureRadiance + "CC"
                : theme.palette.primary.dark,
            },
            fontWeight: 600,
            px: 4,
            py: 1,
          }}
        >
          Back to Login
        </Button>
      </Box>
    );
  }

  return (
    <LoginWrapper>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          px: 2,
          py: 4,
        }}
      >
        <Card
          sx={{
            width: "100%",
            maxWidth: 420,
            boxShadow: 3,
            borderRadius: 2,
          }}
        >
          <CardContent>
            <Typography variant={isMobile ? "h5" : "h2"} gutterBottom fontWeight={600} textAlign="center">
              Reset Password
            </Typography>
            <Typography variant="body2" sx={{ mb: 2 }} textAlign="center" color="text.primary">
              Please enter and confirm your new password below.
            </Typography>

            <form onSubmit={formik.handleSubmit}>
              <GenericFormControl
                id="newPassword"
                label="New Password"
                type={showPassword.new ? "text" : "password"}
                value={formik.values.newPassword}
                onChangeFn={formik.handleChange}
                error={formik.touched.newPassword && !!formik.errors.newPassword}
                helperText={formik.touched.newPassword ? formik.errors.newPassword : undefined}
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => handleToggleShowPassword("new")}
                      edge="end"
                    >
                      {showPassword.new ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                }
              />
              <GenericFormControl
                id="confirmPassword"
                label="Confirm Password"
                type={showPassword.confirm ? "text" : "password"}
                value={formik.values.confirmPassword}
                onChangeFn={formik.handleChange}
                error={formik.touched.confirmPassword && !!formik.errors.confirmPassword}
                helperText={formik.touched.confirmPassword ? formik.errors.confirmPassword : undefined}
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => handleToggleShowPassword("confirm")}
                      edge="end"
                    >
                      {showPassword.confirm ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                }
              />

              <Button
                fullWidth
                variant="contained"
                sx={{
                  bgcolor: theme.palette.custom.brand.azureRadiance,
                  textTransform: "none",
                  fontWeight: 600,
                  py: 1.2,
                  mt: 3,
                }}
                type="submit"
                disabled={formik.isSubmitting}
              >
                {formik.isSubmitting ? "Resetting..." : "Reset Password"}
              </Button>
            </form>

            <Box sx={{ textAlign: "center", mt: 3 }}>
              <Link href="/login" underline="none" fontSize="0.85rem">
                Back to Login
              </Link>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </LoginWrapper>
  );
};

export default ResetPasswordConfirm;
