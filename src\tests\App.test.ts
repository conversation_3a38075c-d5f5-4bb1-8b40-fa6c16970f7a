// If your typescript is showing errors for describe, it, expect, etc, 
// Type > in seatch bar and select "typescript: Reload Project"
// You can find assertions in the https://testing-library.com/docs/react-testing-library/cheatsheet#queries
// You can learn how to write unit test from Programming with Mosh: https://youtu.be/8Xwq35cPwYg?t=1218
describe("App", () => {
  it("should render without crashing", () => {
    // This test is just a placeholder
    expect(true).toBe(true);
  });
});