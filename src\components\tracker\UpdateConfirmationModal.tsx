import React, { useMemo } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
} from "@mui/material";
import {
  FieldChange,
  TimelogData,
  UpdateConfirmationModalProps,
} from "@/types/tracker";

const IGNORED_FIELDS = ["id", "created_at", "updated_at", "user"];

const formatFieldName = (field: string): string => {
  return field
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const defaultFormatValue = (value: unknown): string => {
  if (value === null || value === undefined) return "";
  if (typeof value === "string") return value;
  if (typeof value === "boolean") return value ? "Yes" : "No";
  if (typeof value === "number") return value.toString();
  return JSON.stringify(value);
};

const UpdateConfirmationModal: React.FC<UpdateConfirmationModalProps> = ({
  open,
  onClose,
  onConfirm,
  oldData,
  newData,
  loading,
  formatValue,
}) => {
  const valueFormatter = formatValue || defaultFormatValue;
  const changedFields = useMemo(() => {
    const changes: FieldChange[] = [];
    Object.keys(newData).forEach((key) => {
      if (IGNORED_FIELDS.includes(key)) return;
      const typedKey = key as keyof TimelogData;
      const oldValue = oldData[typedKey];
      const newValue = newData[typedKey];
      if (oldValue !== newValue) {
        changes.push({
          field: key,
          oldValue: valueFormatter(key, oldValue),
          newValue: valueFormatter(key, newValue),
        });
      }
    });
    return changes;
  }, [oldData, newData, valueFormatter]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Confirm Timelog Changes</DialogTitle>
      <DialogContent>
        {changedFields.length > 0 ? (
          <Box sx={{ my: 2 }}>
            <Typography variant="body1" mb={3}>
              You're about to update the following fields:
            </Typography>

            <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
              {changedFields.map(({ field, oldValue, newValue }) => (
                <Box key={field} mb={3}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {formatFieldName(field)}
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mt: 1,
                    }}
                  >
                    <Chip label={oldValue} color="default" variant="outlined" />
                    <Typography variant="body2">→</Typography>
                    <Chip label={newValue} color="primary" variant="filled" />
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        ) : (
          <Typography variant="body1" sx={{ my: 3 }}>
            No changes detected.
          </Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          color="primary"
          variant="contained"
          disabled={loading || changedFields.length === 0}
        >
          {loading ? "Saving..." : "Confirm Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UpdateConfirmationModal;
