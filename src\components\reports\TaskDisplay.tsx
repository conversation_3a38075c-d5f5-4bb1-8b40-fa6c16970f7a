import React from "react";
import { Typography } from "@mui/material";
import { useClickupTaskById } from "@/hooks/tracker";
import { useProjects } from "@/hooks/tracker/useProjects";
import { TaskDisplayProps } from "@/types/reports";
import { useMemo } from "react";

const TaskDisplay: React.FC<TaskDisplayProps> = ({ taskId, projectId }) => {
  const { data } = useProjects("", { ids: projectId ? [projectId] : [] });
  const project = useMemo(() => {
    if (!data?.pages || !projectId) return null;
    return data.pages.flatMap(page => page.results).find(p => p.id === projectId);
  }, [data, projectId]);

  const { task } = useClickupTaskById(project?.clickup_space_id || null, taskId);

  if (task?.name) {
    return (
      <Typography variant="body2" color="text.secondary" component="span">
        Task: {task.name}
      </Typography>
    );
  }

  return (
    <Typography variant="body2" color="text.secondary" component="span">
      Task ID: {taskId}
    </Typography>
  );
};

export default TaskDisplay;
