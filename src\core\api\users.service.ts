import Resource from "./resource";

// Interface for the user response from backend
export interface IUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  groups: {
    id: number;
    name: string;
    name_display: string;
  }[];
  group_permissions: {
    codename: string;
    name: string;
    app_label: string;
  }[];
  is_invited: boolean;
  detail: string | null;
  connected_service: unknown[];
}

// Interface for the paginated response
export interface IUsersResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: IUser[];
}

// Interface for user resume/profile
export interface IUserResume {
  id?: string;
  user_id: string;
  details: IProfileDetailNode[];
}

export interface IProfileDetailNode {
  id?: string;
  value: string;
  parent?: string | null;
  order: number;
  children: IProfileDetailNode[];
}

class UsersResource extends Resource {
  constructor() {
    super("users");
  }

  // Get all active users
  getActiveUsers(params?: { is_active?: boolean; [key: string]: unknown }) {
    return this.list({ is_active: true, ...params });
  }

  // Get user resume/profile
  getUserResume(userId: string) {
    return this.get(`${userId}/resume`);
  }

  // Update user resume/profile
  updateUserResume(userId: string, resumeData: IUserResume) {
    return this.patch(`${userId}/resume`, resumeData);
  }
}

// Export individual classes for backwards compatibility
class TeamResources extends Resource {
  constructor() {
    super("users");
  }
}

class TeamGroupResources extends Resource {
  constructor() {
    super("users/groups");
  }
}

class TeamInviteResources extends Resource {
  constructor() {
    super("users/invite");
  }
}

export default {
  UsersResource,
  TeamResources,
  TeamGroupResources,
  TeamInviteResources,
};
