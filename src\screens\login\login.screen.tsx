import React, { JSX, useState } from "react";
import {
  Box,
  But<PERSON>,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  Link,
  IconButton,
  Typography,
} from "@mui/material";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import InputAdornment from "@mui/material/InputAdornment";
import AuthResource from "@/core/api/auth";
import GenericFormControl from "@/components/form-control/generic-form.component";
import LoginWrapper from "@/components/login-wrapper/login-wrapper.component";
import ToastMessage from "@/components/toast/toast.component";
import { useNavigate } from "react-router-dom";
import { EMAIL_REGEX } from "@/utils/regex";
import * as yup from "yup";
import { useAppDispatch } from "@/store/hooks/hooks";
import { setAuthUser } from "@/store/slice/auth-slice";

const loginValidationSchema = yup.object({
  email: yup
    .string()
    .email("Invalid email format")
    .required("Email is required")
    .matches(EMAIL_REGEX, "Invalid email format"),

  password: yup.string().required("Password is required"),
});

interface ILoginForm {
  email: string;
  password: string;
  stay_logged_in: boolean;
}

const LoginPage = () => {
  const auth = new AuthResource();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [loginForm, setLoginForm] = useState<ILoginForm>({
    email: "",
    password: "",
    stay_logged_in: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.preventDefault();
  };

  const handleMouseUpPassword = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.preventDefault();
  };
  const passwordAdornment: JSX.Element = (
    <InputAdornment position="end">
      <IconButton
        onClick={handleClickShowPassword}
        onMouseDown={handleMouseDownPassword}
        onMouseUp={handleMouseUpPassword}
        edge="end"
      >
        {showPassword ? <VisibilityOff /> : <Visibility />}
      </IconButton>
    </InputAdornment>
  );

  const handleLoginFormSubmit = async (formEvent: React.FormEvent) => {
    formEvent.preventDefault();
    try {
      await loginValidationSchema.validate(loginForm, { abortEarly: false });
      const res = await auth.loginUser({
        email: loginForm.email,
        password: loginForm.password,
        stay_logged_in: loginForm.stay_logged_in,
      });
      dispatch(
        setAuthUser({
          ...res,
          stay_logged_in: loginForm.stay_logged_in,
        }),
      );
      return navigate("/");
    } catch (err) {
      if (err instanceof yup.ValidationError) {
        type ValidationErrors = Partial<Record<keyof ILoginForm, string>>;
        const validationErrors: ValidationErrors = {};

        err.inner.forEach((error) => {
          if (error.path) {
            validationErrors[error.path as keyof ILoginForm] = error.message;
          }
        });

        const firstErrorMessage = Object.values(validationErrors)[0];
        ToastMessage.error(firstErrorMessage as string);
      } else {
        setLoginError("No active account found with the given credentials");
      }

      return false;
    }
  };

  return (
    <LoginWrapper
      children={
        <Box>
          <Card
            sx={{
              boxShadow: 2,
              px: 4,
              py: 3,
              borderRadius: 1.5,
              width: "100%",
              maxWidth: "md",
              mx: "auto",
              border: 1,
              borderColor: "grey.300",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              <Typography variant="h5" gutterBottom fontWeight={600}>
                Log In
              </Typography>
              {loginError && (
                <Box
                  sx={{
                    backgroundColor: "#fdecea",
                    border: "1px solid #f5c2c7",
                    borderRadius: 1,
                    p: 1,
                    mt: 2,
                    mb: 2,
                  }}
                >
                  <Typography variant="body2" color="error">
                    {loginError}
                  </Typography>
                </Box>
              )}
              <form onSubmit={handleLoginFormSubmit}>
                <GenericFormControl
                  id="email-field"
                  label="Enter email"
                  type="text"
                  onChangeFn={(e) =>
                    setLoginForm((prevForm) => ({
                      ...prevForm,
                      email: e.target.value,
                    }))
                  }
                />
                <GenericFormControl
                  id="password-field"
                  label="Password"
                  type={showPassword ? "text" : "password"}
                  onChangeFn={(e) =>
                    setLoginForm((prevForm) => ({
                      ...prevForm,
                      password: e.target.value,
                    }))
                  }
                  endAdornment={passwordAdornment}
                />
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mt: 1,
                    mb: 2,
                  }}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        size="small"
                        sx={{ color: "gray" }}
                        checked={loginForm.stay_logged_in}
                        onChange={(e) =>
                          setLoginForm((prev) => ({
                            ...prev,
                            stay_logged_in: e.target.checked,
                          }))
                        }
                      />
                    }
                    label="Stay logged in"
                    sx={{ m: 0 }}
                  />
                  <Link
                    href="/forgot-password"
                    underline="none"
                    fontSize="0.85rem"
                  >
                    Forgot password ?
                  </Link>
                </Box>
                <Button
                  fullWidth
                  variant="contained"
                  sx={{
                    bgcolor: "#0084ff",
                    textTransform: "none",
                    fontWeight: 600,
                    py: 1,
                  }}
                  type="submit"
                >
                  LOG IN
                </Button>
              </form>
            </CardContent>
          </Card>
          <Box
            mt={2}
            fontSize="0.8rem"
            sx={{ display: "flex", justifyContent: "center" }}
          >
            <Link href="#" underline="hover" mr={1}>
              PRIVACY POLICY
            </Link>
            |
            <Link href="#" underline="hover" ml={1}>
              USER NOTICE
            </Link>
          </Box>
        </Box>
      }
    ></LoginWrapper>
  );
};

export default LoginPage;
