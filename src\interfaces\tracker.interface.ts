import { FormValues } from "@/types/tracker";

export interface TrackerFormProps {
  onTrackerStateChange?: (isTracking: boolean) => void;
}

export interface UseTrackerStateProps {
  userId?: string;
  onTrackerStateChange?: (isTracking: boolean) => void;
  setFieldValueRef?: React.MutableRefObject<((field: string, value: string | boolean) => void) | null>;
}

export interface TrackerFormHandle {
  restartWithValues: (values: FormValues) => Promise<void>;
}
