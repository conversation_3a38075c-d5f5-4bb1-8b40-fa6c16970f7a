import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import EmployeeProfileService from "../api/employee-profile.service";
import { EmployeeSearchService, IEmployeeSearchResult } from "../api/employee-search.service";
import { IEmployeeProfile, IEmployeeProfilePayload } from "@/interfaces/employeeProfile.interface";

// Hook to get all employees
export const useEmployees = () => {
  return useQuery<IEmployeeSearchResult[]>({
    queryKey: ["employees"],
    queryFn: EmployeeSearchService.getAllEmployees,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
};

// Hook to get a specific employee's resume
export const useEmployeeResume = (userId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ["employeeResume", userId],
    queryFn: () => EmployeeProfileService.getEmployeeProfile(userId),
    enabled,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10, // 10 minutes
    // Don't retry on error - if no resume exists, that's expected
    retry: false,
  });
};

// Hook to get all employee profiles (combines employees with their resumes)
export const useEmployeeProfiles = () => {
  const { data: employees = [], isLoading: employeesLoading, error: employeesError } = useEmployees();

  const employeeProfiles: IEmployeeProfile[] = (employees as IEmployeeSearchResult[]).map(employee => {
    // Create a basic profile structure for each employee
    return {
      id: `profile-${employee.id}`,
      selectedEmployee: {
        id: employee.id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
        department: employee.department,
        jobTitle: employee.jobTitle,
        contactNumber: employee.contactNumber,
        address: employee.address,
      },
      startDate: new Date().toISOString(),
      status: "active",
      candidateBackground: {
        coreLanguages: [],
        coreFrameworks: [],
        additionalSkills: [],
      },
      availability: "Immediate",
      education: [],
      workExperience: [],
      languages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  });

  return {
    data: employeeProfiles,
    isLoading: employeesLoading,
    error: employeesError,
  };
};

// Hook to save/update employee profile
export const useEmployeeProfileMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      profileData,
      isEditing,
      userId,
    }: {
      profileData: IEmployeeProfilePayload;
      isEditing: boolean;
      userId: string;
    }) => {
      if (isEditing) {
        return await EmployeeProfileService.updateEmployeeProfile(userId, profileData);
      } else {
        return await EmployeeProfileService.createEmployeeProfile(profileData);
      }
    },
    onSuccess: (_, variables) => {
      // Invalidate specific employee resume
      queryClient.invalidateQueries({ queryKey: ["employeeResume", variables.userId] });
      // Invalidate all employees to refresh the list
      queryClient.invalidateQueries({ queryKey: ["employees"] });
    },
  });
};
