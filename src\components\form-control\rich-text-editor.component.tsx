import React from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import Link from "@tiptap/extension-link";
import { Box, Typography, Tooltip, Button } from "@mui/material";
import FormatBoldIcon from "@mui/icons-material/FormatBold";
import FormatItalicIcon from "@mui/icons-material/FormatItalic";
import FormatUnderlinedIcon from "@mui/icons-material/FormatUnderlined";
import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import FormatListNumberedIcon from "@mui/icons-material/FormatListNumbered";
import LooksOneIcon from "@mui/icons-material/LooksOne";
import LooksTwoIcon from "@mui/icons-material/LooksTwo";

interface TextEditorProps {
  value?: string;
  onChange?: (content: string) => void;
  label?: string;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  tooltipText?: string;
}

const TextEditor: React.FC<TextEditorProps> = ({
  value = "",
  onChange,
  label,
  placeholder,
  height = 200,
  disabled = false,
  required = false,
  error = false,
  helperText,
  tooltipText = "Enter your text here",
}) => {
  const editor = useEditor({
    extensions: [StarterKit, Underline, Link.configure({ openOnClick: false })],
    content: value,
    editable: !disabled,
    onUpdate: ({ editor }) => {
      if (onChange) {
        onChange(editor.getHTML());
      }
    },
  });

  const setBold = () => editor?.chain().focus().toggleBold().run();
  const setItalic = () => editor?.chain().focus().toggleItalic().run();
  const setUnderline = () => editor?.chain().focus().toggleUnderline().run();
  const setBulletList = () => editor?.chain().focus().toggleBulletList().run();
  const setOrderedList = () => editor?.chain().focus().toggleOrderedList().run();
  const setHeading1 = () => editor?.chain().focus().toggleHeading({ level: 1 }).run();
  const setHeading2 = () => editor?.chain().focus().toggleHeading({ level: 2 }).run();

  return (
    <Box sx={{ width: "100%" }}>
      {label && (
        <Typography
          variant="body2"
          component="label"
          sx={{
            display: "block",
            mb: 1,
            fontWeight: 500,
            color: error ? "error.main" : "text.primary",
          }}
        >
          {label}
          {required && (
            <Typography component="span" sx={{ color: "error.main", ml: 0.5 }}>
              *
            </Typography>
          )}
        </Typography>
      )}
      <Tooltip title={tooltipText} arrow placement="top">
        <Box
          sx={{
            border: error ? "1px solid" : "1px solid",
            borderColor: error ? "error.main" : "grey.300",
            borderRadius: 1,
            "&:hover": {
              borderColor: error ? "error.main" : "primary.main",
            },
            "&:focus-within": {
              borderColor: error ? "error.main" : "primary.main",
              borderWidth: 2,
            },
          }}
        >
          <Box sx={{ display: "flex", gap: 1, p: 1, borderBottom: "1px solid grey.300" }}>
            <Button onClick={setBold} disabled={!editor?.isEditable}>
              <FormatBoldIcon />
            </Button>
            <Button onClick={setItalic} disabled={!editor?.isEditable}>
              <FormatItalicIcon />
            </Button>
            <Button onClick={setUnderline} disabled={!editor?.isEditable}>
              <FormatUnderlinedIcon />
            </Button>
            <Button onClick={setHeading1} disabled={!editor?.isEditable}>
              <LooksOneIcon />
            </Button>
            <Button onClick={setHeading2} disabled={!editor?.isEditable}>
              <LooksTwoIcon />
            </Button>
            <Button onClick={setBulletList} disabled={!editor?.isEditable}>
              <FormatListBulletedIcon />
            </Button>
            <Button onClick={setOrderedList} disabled={!editor?.isEditable}>
              <FormatListNumberedIcon />
            </Button>
          </Box>
          <EditorContent
            editor={editor}
            style={{
              minHeight: `${height - 50}px`,
              padding: "8px",
              fontFamily: "Helvetica, Arial, sans-serif",
              fontSize: "14px",
              outline: "none",
            }}
          />
          {placeholder && !value && (
            <Box
              sx={{
                position: "absolute",
                top: "60px",
                left: "12px",
                color: "grey.500",
                pointerEvents: "none",
              }}
            >
              {placeholder}
            </Box>
          )}
        </Box>
      </Tooltip>
      {helperText && (
        <Typography
          variant="caption"
          sx={{
            display: "block",
            mt: 0.5,
            color: error ? "error.main" : "text.secondary",
          }}
        >
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default TextEditor;
