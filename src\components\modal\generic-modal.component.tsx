import CloseIcon from "@mui/icons-material/Close";
import { Backdrop, Box, Fade, IconButton, Modal, Typography } from "@mui/material";
import React from "react";
import { GenericModalProps } from "@/interfaces/modal-props.interface";

const GenericModal: React.FC<GenericModalProps> = ({ setting, title, children, height }) => {
  return (
    <Modal
      open={setting.open}
      onClose={setting.onClose}
      closeAfterTransition
      slots={{ backdrop: Backdrop }}
      slotProps={{ backdrop: { timeout: 500 } }}
    >
      <Fade in={setting.open}>
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            minHeight: height || "100%",
            transform: "translate(-50%, -50%)",
            width: "90%",
            maxWidth: 600,
            bgcolor: "background.paper",
            boxShadow: 24,
            p: 4,
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            {title && (
              <Typography variant="h3" fontWeight="bold">
                {title}
              </Typography>
            )}
            <IconButton onClick={setting.onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
          {children}
        </Box>
      </Fade>
    </Modal>
  );
};

export default GenericModal;
